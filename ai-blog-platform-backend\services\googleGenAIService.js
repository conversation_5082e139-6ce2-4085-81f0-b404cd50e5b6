// services/googleGenAIService.js
const { GoogleGenAI } = require('@google/genai');
const path = require('path');
require('dotenv').config();

class GoogleGenAIService {
  constructor() {
    // Initialize Google Gen AI client
    this.project = process.env.GOOGLE_CLOUD_PROJECT ||
                   process.env.VERTEX_AI_PROJECT ||
                   process.env.VERTEX_AI_PROJECT_ID;
    this.location = process.env.GOOGLE_CLOUD_LOCATION ||
                    process.env.VERTEX_AI_LOCATION ||
                    process.env.VERTEX_AI_REGION ||
                    'us-central1';

    if (!this.project) {
      console.warn('⚠️ GOOGLE_CLOUD_PROJECT not set. Google Gen AI service disabled.');
      this.client = null;
      return;
    }

    try {
      // Set up authentication
      const serviceAccountPath = process.env.GOOGLE_APPLICATION_CREDENTIALS ||
                                path.join(__dirname, '..', 'service_account_key.json');

      if (!process.env.GOOGLE_APPLICATION_CREDENTIALS) {
        process.env.GOOGLE_APPLICATION_CREDENTIALS = serviceAccountPath;
      }

      // Initialize Google Gen AI with Vertex AI backend
      this.client = new GoogleGenAI({
        vertexai: true,
        project: this.project,
        location: this.location,
      });

      // Models
      this.textModel = 'gemini-2.5-flash';
      this.imageModel = 'imagen-3.0-generate-002';

      console.log(`✅ Google Gen AI Service initialized successfully!`);
      console.log(`   Project: ${this.project}`);
      console.log(`   Location: ${this.location}`);
      console.log(`   Text Model: ${this.textModel}`);
      console.log(`   Image Model: ${this.imageModel}`);
    } catch (error) {
      console.error('❌ Failed to initialize Google Gen AI Service:', error.message);
      this.client = null;
    }
  }

  /**
   * Generate text content using Gemini
   */
  async generateContent(prompt, options = {}) {
    if (!this.client) {
      throw new Error('Google Gen AI service not initialized');
    }

    try {
      const {
        temperature = 0.7,
        maxTokens = 4096, // Increased from 2048 to avoid MAX_TOKENS truncation
        model = this.textModel
      } = options;

      console.log(`🤖 Generating content with ${model}...`);
      console.log(`📝 Prompt length: ${prompt.length} characters`);

      // Try different API call formats for Google Gen AI
      let response;

      try {
        // Format 1: Standard Google Gen AI format
        response = await this.client.models.generateContent({
          model: model,
          contents: [{
            role: 'user',
            parts: [{ text: prompt }]
          }],
          generationConfig: {
            temperature: temperature,
            maxOutputTokens: maxTokens,
          },
        });
        console.log('✅ Used format 1: Standard Google Gen AI format');
      } catch (error1) {
        console.log('⚠️ Format 1 failed, trying format 2:', error1.message);

        try {
          // Format 2: Simple format
          response = await this.client.models.generateContent({
            model: model,
            contents: prompt,
            config: {
              temperature: temperature,
              maxOutputTokens: maxTokens,
            },
          });
          console.log('✅ Used format 2: Simple format');
        } catch (error2) {
          console.log('⚠️ Format 2 failed, trying format 3:', error2.message);

          // Format 3: Direct text format
          response = await this.client.models.generateContent(prompt, {
            temperature: temperature,
            maxOutputTokens: maxTokens,
          });
          console.log('✅ Used format 3: Direct text format');
        }
      }

      // Debug the response structure
      console.log('🔍 Debug - Response candidates:', response?.candidates?.length || 0);
      if (response?.candidates?.[0]) {
        const candidate = response.candidates[0];
        console.log('🔍 Debug - Candidate structure:', {
          hasContent: !!candidate.content,
          contentKeys: candidate.content ? Object.keys(candidate.content) : [],
          finishReason: candidate.finishReason,
          hasParts: candidate.content?.parts ? candidate.content.parts.length : 0
        });

        if (candidate.content?.parts) {
          console.log('🔍 Debug - Parts structure:', candidate.content.parts.map((part, i) => ({
            index: i,
            hasText: !!part.text,
            textLength: part.text ? part.text.length : 0,
            keys: Object.keys(part)
          })));
        }
      }

      // Handle different possible response structures
      let generatedText = null;

      if (response?.text) {
        generatedText = response.text;
        console.log('✅ Found text in response.text');
      } else if (response?.candidates?.[0]?.content?.parts?.[0]?.text) {
        generatedText = response.candidates[0].content.parts[0].text;
        console.log('✅ Found text in candidates[0].content.parts[0].text');
      } else if (response?.candidates?.[0]?.content?.text) {
        generatedText = response.candidates[0].content.text;
        console.log('✅ Found text in candidates[0].content.text');
      } else if (response?.candidates?.[0]?.text) {
        generatedText = response.candidates[0].text;
        console.log('✅ Found text in candidates[0].text');
      } else if (typeof response === 'string') {
        generatedText = response;
        console.log('✅ Response is string');
      } else if (response?.candidates?.[0]?.content?.parts) {
        // Try to extract text from parts array
        const parts = response.candidates[0].content.parts;
        if (Array.isArray(parts) && parts.length > 0) {
          generatedText = parts.map(part => part.text || '').filter(text => text).join('');
          console.log(`✅ Found text in parts array: ${parts.length} parts, ${generatedText.length} chars`);
        }
      }

      // Check for MAX_TOKENS finish reason and handle appropriately
      if (!generatedText && response?.candidates?.[0]?.finishReason === 'MAX_TOKENS') {
        console.warn('⚠️ Response truncated due to MAX_TOKENS, trying with reduced prompt...');
        throw new Error('Response truncated due to MAX_TOKENS - will retry with fallback');
      }

      if (!generatedText) {
        console.error('❌ No text found in response:', response);
        throw new Error('No content generated - unexpected response structure');
      }

      console.log(`✅ Generated content: ${generatedText.length} characters`);

      return {
        success: true,
        content: generatedText,
        model: model,
        usage: response.usage || {}
      };

    } catch (error) {
      console.error('Content generation error:', error);
      throw error;
    }
  }

  /**
   * Generate images using Imagen 3
   */
  async generateImages(prompt, options = {}) {
    if (!this.client) {
      throw new Error('Google Gen AI service not initialized');
    }

    try {
      const {
        numberOfImages = 1,
        aspectRatio = '1:1',
        safetyFilterLevel = 'block_some',
        includeRaiReason = true,
        outputMimeType = 'image/jpeg'
      } = options;

      console.log(`🎨 Generating images with ${this.imageModel}...`);
      console.log(`📝 Prompt: ${prompt}`);

      const response = await this.client.models.generateImages({
        model: this.imageModel,
        prompt: prompt,
        config: {
          numberOfImages: numberOfImages,
          aspectRatio: aspectRatio,
          safetyFilterLevel: safetyFilterLevel,
          includeRaiReason: includeRaiReason,
          outputMimeType: outputMimeType,
        },
      });

      if (!response || !response.generatedImages || response.generatedImages.length === 0) {
        throw new Error('No images generated');
      }

      console.log(`✅ Generated ${response.generatedImages.length} image(s)`);

      return {
        success: true,
        images: response.generatedImages,
        model: this.imageModel,
        prompt: prompt
      };

    } catch (error) {
      console.error('Image generation error:', error);
      throw error;
    }
  }

  /**
   * Stream content generation
   */
  async generateContentStream(prompt, options = {}) {
    if (!this.client) {
      throw new Error('Google Gen AI service not initialized');
    }

    try {
      const {
        temperature = 0.7,
        maxTokens = 2048,
        model = this.textModel
      } = options;

      console.log(`🌊 Streaming content with ${model}...`);

      const response = await this.client.models.generateContentStream({
        model: model,
        contents: prompt,
        config: {
          temperature: temperature,
          maxOutputTokens: maxTokens,
        },
      });

      return response;

    } catch (error) {
      console.error('Streaming content generation error:', error);
      throw error;
    }
  }

  /**
   * Count tokens in content
   */
  async countTokens(content, model = this.textModel) {
    if (!this.client) {
      throw new Error('Google Gen AI service not initialized');
    }

    try {
      const response = await this.client.models.countTokens({
        model: model,
        contents: content,
      });

      return {
        totalTokens: response.totalTokens,
        model: model
      };

    } catch (error) {
      console.error('Token counting error:', error);
      throw error;
    }
  }

  /**
   * Check if service is available
   */
  isAvailable() {
    return this.client !== null;
  }

  /**
   * Get service status
   */
  getStatus() {
    return {
      available: this.isAvailable(),
      project: this.project,
      location: this.location,
      textModel: this.textModel,
      imageModel: this.imageModel,
      service: 'Google Gen AI'
    };
  }
}

module.exports = GoogleGenAIService;
