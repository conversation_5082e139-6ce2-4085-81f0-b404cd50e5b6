const vertexService = require('./geminiService'); // Renamed for clarity (uses Vertex SDK)
const crypto = require('crypto');

class SEOOptimizationService {
  constructor() {
    this.rankMathCriteria = {
      // Basic SEO Requirements for 85-100/100 score
      focusKeywordInTitle: { weight: 15, required: true },
      focusKeywordInMetaDescription: { weight: 10, required: true },
      focusKeywordInURL: { weight: 10, required: true },
      focusKeywordInFirst10Percent: { weight: 15, required: true },
      focusKeywordInContent: { weight: 10, required: true },
      contentLength: { weight: 10, minWords: 1102, required: true },
      titleReadability: { weight: 10, maxLength: 60, required: true },
      contentReadability: { weight: 10, required: true },
      metaDescriptionLength: { weight: 5, minLength: 140, maxLength: 160 },
      keywordDensity: { weight: 5, minDensity: 0.5, maxDensity: 2.5 },
      internalLinks: { weight: 5, minLinks: 2 },
      externalLinks: { weight: 5, minLinks: 1 }
    };
  }

  /**
   * Generate SEO-optimized content that scores 85-100/100 in RankMath
   * @param {Object} contentData - Content generation parameters
   * @returns {Object} SEO-optimized content structure
   */
  async generateSEOOptimizedContent(contentData) {
    const {
      selectedKeyword,
      selectedH1,
      selectedMetaTitle,
      selectedMetaDescription,
      companyName,
      companyContext,
      wordCount,
      articleFormat,
      targetAudience,
      objective = 'Education'
    } = contentData;

    console.log(`🎯 GENERATING SEO-OPTIMIZED CONTENT FOR RANKMATH 85-100/100 SCORE`);
    console.log(`   Focus Keyword: "${selectedKeyword}"`);
    console.log(`   Target Word Count: ${wordCount}`);
    console.log(`   Article Format: ${articleFormat}`);
    console.log(`   Target Audience: ${targetAudience}`);
    console.log(`   Objective: ${objective}`);

    // Step 1: Optimize meta data for RankMath
    const optimizedMeta = await this.optimizeMetaData({
      keyword: selectedKeyword,
      h1: selectedH1,
      metaTitle: selectedMetaTitle,
      metaDescription: selectedMetaDescription,
      companyName
    });

  // Derive a draftId seed (optional) to support deterministic H2 variation on regenerate
  const draftId = contentData.draftId || `${selectedH1 || ''}-${selectedKeyword}`;
  // Step 2: Generate keyword-optimized content structure (pass selectedH1 so we can detect listicle titles like "5 ways to...")
  const contentStructure = await this.generateKeywordOptimizedStructure(selectedKeyword, wordCount, selectedH1, draftId);

    // Step 3: Generate real working links for the content and curated external references
    let realLinks = { inboundLinks: [], outboundLinks: [] };
    let externalReferences = [];
    try {
      const linkService = require('./linkService');
      realLinks = await linkService.generateInboundOutboundLinks(selectedKeyword, companyName);
  // Validate returned links to remove any dead ones
  realLinks.inboundLinks = await this.validateUrls(realLinks.inboundLinks || []);
  realLinks.outboundLinks = await this.validateUrls(realLinks.outboundLinks || []);
  console.log(`🔗 Generated ${realLinks.inboundLinks.length} real internal and ${realLinks.outboundLinks.length} real external links (validated)`);

      // Attempt to fetch curated external references (news/articles) to use as citations
      try {
        let refs = [];
        try {
          const serpService = require('./serpService');
          if (serpService && serpService.searchNews) {
            refs = await serpService.searchNews(selectedKeyword, { limit: 6 });
          }
        } catch (e) {
          // ignore
        }

        if ((!refs || refs.length === 0)) {
          try {
            const perplexityService = require('./perplexityService');
            if (perplexityService && perplexityService.searchArticles) {
              refs = await perplexityService.searchArticles(selectedKeyword, { limit: 6 });
            }
          } catch (e) {
            // ignore
          }
        }

        if ((!refs || refs.length === 0) && linkService.fetchExternalReferences) {
          refs = await linkService.fetchExternalReferences(selectedKeyword, companyName, { limit: 6 });
        }

        externalReferences = (refs || []).map((r, i) => ({ id: `R${i + 1}`, title: r.title || r.text || r.headline || 'Source', url: r.url || r.link || r.source, source: r.source || r.provider || 'external', publishedAt: r.publishedAt || r.date, snippet: r.snippet || r.summary || '' }));

        // Validate URLs and drop dead links
        externalReferences = await this.validateUrls(externalReferences);
        console.log(`📰 Retrieved and validated ${externalReferences.length} external references`);
      } catch (err) {
        console.warn('⚠️ External reference fetch failed:', err.message || err);
      }
    } catch (error) {
      console.warn('⚠️ LinkService not available, using fallback links');
    }

    // Step 4: Create SEO-compliant content blocks with keyword criteria, real links and external references
    const contentBlocks = await this.generateSEOContentBlocks(
      contentStructure,
      selectedKeyword,
      companyName,
      companyContext,
      { articleFormat, targetAudience, objective },
      realLinks,
      externalReferences
    );

    // POST-PROCESS: apply recommendations and cleanup before validation
    //  - aggressively strip any embedded JSON-like fragments inside HTML
    //  - insert validated links inline between paragraphs (internal + external)
    //  - ensure meta description length 140-160 chars
    //  - ensure at least one H2 contains the exact keyword
    //  - ensure at least one image alt contains the keyword
    //  - ensure at least 2 internal links exist in content (inject if needed)
    //  - ensure total word count >= rankMath criterion (1102)

    // Clean embedded JSON fragments inside each block
    for (const b of contentBlocks) {
      if (b && typeof b.content === 'string') {
        b.content = this.removeEmbeddedJsonFragments(b.content);
      }
    }

    // Insert contextual links inline into paragraph blocks
    await this.insertLinksIntoParagraphs(contentBlocks, realLinks);

    // Fix meta description length
    optimizedMeta.metaDescription = this.fixMetaDescription(optimizedMeta.metaDescription || selectedMetaDescription, companyName);

    // Ensure at least one H2 contains the exact keyword
    this.ensureH2WithKeyword(contentBlocks, selectedKeyword);

    // Ensure at least one image alt contains the keyword (insert tiny data URI if needed)
    this.ensureImageAltWithKeyword(contentBlocks, selectedKeyword);

    // Ensure minimum internal links (inject uses of inbound links if under threshold)
    this.ensureInternalLinksCount(contentBlocks, realLinks, 2, selectedKeyword);

    // Ensure total word count meets minimum (1102) by expanding conclusion or adding to last paragraph
    await this.ensureMinimumWordCount(contentBlocks, 1102, selectedKeyword, companyContext);

    // FINAL SANITIZATION: remove any lingering [object Object] artifacts or similar object-to-string leaks
    const sanitizeObjectArtifacts = (str) => {
      if (!str || typeof str !== 'string') return str;
      // Remove common object stringification artifacts
      let s = str.replace(/\[object\s+Object\]/g, '');
      s = s.replace(/,?\s*\[object\s+Object\],?/g, '');
      s = s.replace(/\(object Object\)/g, '');
      s = s.replace(/\{\s*object Object\s*\}/g, '');
      // collapse extra punctuation leftover like ', ,' or ' ,.'
      s = s.replace(/,\s*,/g, ',');
      s = s.replace(/,\s*\./g, '.');
  // Remove common LLM prompt artifacts like '300 words', '400 words', or 'Write between 300 and 400 words.'
  s = s.replace(/\b\d{2,4}\s+words\b/gi, '');
  s = s.replace(/Write between \d{2,4} and \d{2,4} words\.?/gi, '');
  s = s.replace(/Write between \d{2,4} and \d{2,4} words and report the measured word_count in JSON\.?/gi, '');
  // Remove percent-encoded object tokens seen in URLs
  s = s.replace(/\[object%20Object\]/gi, '');
      s = s.replace(/\s{2,}/g, ' ').trim();
      return s;
    };

    for (const b of contentBlocks) {
      if (!b) continue;
      if (typeof b.content === 'string') b.content = sanitizeObjectArtifacts(b.content);
      if (typeof b.contentHtml === 'string') b.contentHtml = sanitizeObjectArtifacts(b.contentHtml);
      // update wordCount if content changed
      if (typeof b.content === 'string') b.wordCount = this.countWordsFromHtml(b.content);
    }

    // Step 4: Validate SEO compliance
    const seoValidation = this.validateSEOCompliance(contentBlocks, selectedKeyword, optimizedMeta);

    return {
      optimizedMeta,
      contentBlocks,
      seoValidation,
      estimatedRankMathScore: seoValidation.score,
      seoRecommendations: seoValidation.recommendations
    };
  }

  /**
   * Optimize meta data for maximum RankMath score
   */
  async optimizeMetaData(metaData) {
    const { keyword, h1, metaTitle, metaDescription, companyName } = metaData;

    const optimizationPrompt = `METADATA:
{
  "keyword": "${keyword}",
  "h1": "${h1}",
  "metaTitle": "${metaTitle}",
  "metaDescription": "${metaDescription}",
  "company": "${companyName}",
  "objective": "Optimize for RankMath and human readers"
}

INSTRUCTIONS:
You are a senior SEO editor writing for a professional solar audience. Produce optimized metadata that reads like a human headline and meets RankMath best practices.

REQUIREMENTS (strict):
- H1 must include the exact focus keyword phrase as an exact match in a prime position (preferably at the start).
- Meta Title should include the exact focus keyword phrase near the beginning and may include the company name. Length: 50–60 characters recommended.
- Meta Description must include the exact focus keyword phrase within the first 20 characters and read like a persuasive, human sentence. Length: 140–160 characters.
- Suggest a URL slug using only the keyword (hyphenated, lowercase) and under 50 characters.
- Do NOT invent years or placeholder tokens. Do NOT include the words "AI" or "generated".

OUTPUT (JSON only):
Return a single valid JSON object with these keys exactly:
{
  "optimizedH1": "...",
  "optimizedMetaTitle": "...",
  "optimizedMetaDescription": "...",
  "optimizedSlug": "...",
  "keywordPlacementNotes": "short guidance where keyword appears (intro, first H2, meta)",
  "exactMatches": { "h1": true|false, "metaTitle": true|false, "metaDescription": true|false },
  "estimatedRankMathScore": "number-or-range",
  "notes": "short editorial notes if any"
}

Be concise and return only JSON. Ensure the three exactMatches fields reflect whether the exact keyword phrase appears in the requested prime positions.`;

    try {
  const response = await vertexService.generateContent(optimizationPrompt, { name: companyName });
  const cleaned = response.content.replace(/```json|```/g, '');
  const optimized = JSON.parse(cleaned);
      
      // Validate and ensure compliance
      return {
        h1: optimized.optimizedH1 || h1,
        metaTitle: optimized.optimizedMetaTitle || metaTitle,
        metaDescription: optimized.optimizedMetaDescription || metaDescription,
        slug: optimized.optimizedSlug || this.generateSEOSlug(keyword),
        keywordPlacement: optimized.keywordPlacement,
        estimatedScore: optimized.estimatedScore
      };
    } catch (error) {
      console.error('Meta optimization failed:', error);
      return {
        h1: h1,
        metaTitle: metaTitle,
        metaDescription: metaDescription,
        slug: this.generateSEOSlug(keyword),
        keywordPlacement: 'Standard placement',
        estimatedScore: 'Unable to estimate'
      };
    }
  }

  /**
   * Generate keyword-optimized content structure with enhanced RankMath compliance
   */
  // Optional seeded H2 variation using a draftId to make regenerations reproducible
  seededRandom(seed) {
    // simple mulberry32 PRNG
    let t = seed >>> 0;
    return function() {
      t += 0x6D2B79F5;
      let r = Math.imul(t ^ t >>> 15, 1 | t);
      r ^= r + Math.imul(r ^ r >>> 7, 61 | r);
      return ((r ^ r >>> 14) >>> 0) / 4294967296;
    };
  }

  // Convert a string into a 32-bit seed integer
  hashStringToSeed(str) {
    if (!str) return crypto.randomInt(0, 2 ** 31 - 1);
    let h = 2166136261 >>> 0;
    for (let i = 0; i < str.length; i++) {
      h ^= str.charCodeAt(i);
      h = Math.imul(h, 16777619) >>> 0;
    }
    return h >>> 0;
  }

  async generateKeywordOptimizedStructure(keyword, wordCount, selectedH1 = '', draftId = '') {
    // Dynamic section count based on target length - allows flexible number of sections
    const approxIntroPct = 0.12;
    const approxConclusionPct = 0.12;
    const remainingPct = 1 - (approxIntroPct + approxConclusionPct);

    // Derive number of sections: one section per ~200-300 words, clamped to [3,14]
    const approxPerSection = 220;
    const numSections = Math.max(3, Math.min(14, Math.round((wordCount * remainingPct) / approxPerSection)));

    const introWords = Math.round(wordCount * approxIntroPct);
    const conclusionWords = Math.round(wordCount * approxConclusionPct);
    const sectionWords = Math.round((wordCount * remainingPct) / numSections);

    console.log(`📊 Word distribution: Intro(${introWords}) + ${numSections}×Sections(${sectionWords}) + Conclusion(${conclusionWords}) = approx ${introWords + (sectionWords * numSections) + conclusionWords} words`);

  const titleTemplates = [
      `What is ${keyword}? Technical Overview`,
      `Top Benefits of ${keyword} Systems`,
      `${keyword} Installation Process & Requirements`,
      `${keyword} Maintenance & Long-term Performance`,
      `${keyword} Project Planning & Timeline`,
      `Design Considerations for ${keyword}`,
      `Cost Drivers for ${keyword}`,
      `Permitting & Regulatory Considerations for ${keyword}`,
      `Performance Optimization for ${keyword}`,
      `Common Mistakes in ${keyword} Design`
    ];

    const mainSections = [];

    // If the selected H1 looks like a 'X ways to ...' title (listicle), create numbered, varied list sections
    const listMatch = (selectedH1 || '').match(/(\b(\d{1,2})\b)\s*(ways|ways to|ways of)\b/i);
    if (listMatch) {
      const count = Math.max(2, Math.min(20, parseInt(listMatch[2], 10) || numSections));
      // use deterministic randomness if draftId supplied
      const seed = draftId ? this.hashStringToSeed(draftId) : crypto.randomInt(0, 2 ** 31 - 1);
      const rnd = this.seededRandom(seed);
      const verbs = ['Reduce', 'Improve', 'Avoid', 'Maximize', 'Simplify', 'Boost', 'Cut', 'Speed up', 'Protect', 'Choose', 'Optimize', 'Streamline'];
      const nouns = ['costs', 'energy bills', 'downtime', 'maintenance', 'efficiency', 'installation time', 'performance', 'waste', 'risks', 'uptime', 'errors', 'losses'];
      const connectors = ['with', 'using', 'through', 'by leveraging'];
      for (let i = 1; i <= count; i++) {
        const v = verbs[Math.floor(rnd() * verbs.length)];
        const n = nouns[Math.floor(rnd() * nouns.length)];
        const c = connectors[Math.floor(rnd() * connectors.length)];
        let heading = `${i}. How to ${v.toLowerCase()} ${n} ${c} ${keyword}`;
        if (!new RegExp(`\\b${keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'i').test(heading)) {
          heading = `${i}. ${keyword} - ${v} ${n}`;
        }
        mainSections.push({
          heading,
          headingStyle: 'H2 listicle style (human-friendly)',
          wordCount: Math.round(sectionWords),
          keywordRequirement: 'Mention the focus keyword naturally within section content',
          purpose: `Numbered item ${i} in listicle`,
          contentStyle: 'Short, blog-friendly actionable item with a quick example',
          uniqueContent: `Actionable guidance for step ${i}`
        });
      }
      // Guarantee exact list count
      while (mainSections.length > count) mainSections.pop();
    } else {
      // Non-list articles: create exactly numSections headings, ensuring the keyword appears in each heading and headings vary across regenerations
      const seed = draftId ? this.hashStringToSeed(draftId) : crypto.randomInt(0, 2 ** 31 - 1);
      const rnd = this.seededRandom(seed + 1);
      for (let i = 0; i < numSections; i++) {
        let base = titleTemplates[i % titleTemplates.length] || `${keyword} - Section ${i + 1}`;
        if (!new RegExp(`\\b${keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'i').test(base)) {
          base = `${base} for ${keyword}`;
        }
        // add small randomized suffix to avoid identical H2s across regenerations (seeded)
        if (Math.floor(rnd() * 4) === 0) {
          const suffixes = ['Key considerations', 'Quick tips', 'What to know', 'Practical guide', 'Expert advice'];
          base = `${base} — ${suffixes[Math.floor(rnd() * suffixes.length)]}`;
        }
        mainSections.push({
          heading: base,
          headingStyle: 'H2 with #FBD46F color, Roboto font, Semi Bold (600), proper spacing',
          wordCount: sectionWords,
          keywordRequirement: 'Include keyword within section content',
          purpose: 'Provide focused guidance for this subtopic',
          contentStyle: 'Practical guidance, examples, specs',
          uniqueContent: `Detailed exploration of ${base}`
        });
      }
    }

    const structure = {
      introduction: {
        wordCount: introWords,
        keywordRequirement: 'Must include focus keyword in first 50 words (CRITICAL for RankMath)',
        purpose: 'Hook reader, establish keyword relevance, and preview article value',
        styling: 'Professional paragraph with Roboto font, engaging opener, proper structure'
      },
      mainSections,
  conclusion: {
        heading: `${keyword}: Making the Right Choice for Your Project`,
        headingStyle: 'H2 with #FBD46F color, Roboto font, Semi Bold (600), proper spacing',
        wordCount: Math.round(wordCount * 0.12), // 12% of target (no cap)
        keywordRequirement: 'Include exact keyword phrase and strong call-to-action language',
        purpose: 'Summarize key points, reinforce benefits, and encourage immediate action',
        contentStyle: 'Strong conclusion with clear CTA, company branding, urgency language'
      }
    };

    return structure;
  }

  /**
   * Generate SEO-compliant content blocks
   */
  async generateSEOContentBlocks(structure, keyword, companyName, companyContext = {}, keywordCriteria = {}, realLinks = { inboundLinks: [], outboundLinks: [] }, externalReferences = []) {
    const { articleFormat = 'guide', targetAudience = 'Solar industry professionals', objective = 'Education' } = keywordCriteria;
    // Normalize companyContext fields to avoid object-to-string leaks in prompts
    if (companyContext && typeof companyContext.servicesOffered !== 'string') {
      if (Array.isArray(companyContext.servicesOffered)) companyContext.servicesOffered = companyContext.servicesOffered.join(', ');
      else if (companyContext.servicesOffered && typeof companyContext.servicesOffered === 'object') companyContext.servicesOffered = Object.keys(companyContext.servicesOffered).join(', ');
      else companyContext.servicesOffered = String(companyContext.servicesOffered || 'Solar Design, Engineering, Permitting, Installation Support');
    }
    const blocks = [];
    let blockId = 1;
  const usedCitations = []; // collect citations returned by the model across blocks

    // Diagnostic: log incoming structure summary to help debug missing mainSections
    try { console.log(`🔍 Incoming structure: introWC=${structure.introduction&&structure.introduction.wordCount||0}, mainSections=${(structure.mainSections||[]).length}, conclusionWC=${structure.conclusion&&structure.conclusion.wordCount||0}`); } catch(e){}

    // If mainSections are missing or empty, attempt to regenerate a proper structure
    if (!structure.mainSections || !Array.isArray(structure.mainSections) || structure.mainSections.length === 0) {
      try {
        // Estimate a target word count: introduce + some main content + conclusion
        const introWC = (structure.introduction && structure.introduction.wordCount) ? structure.introduction.wordCount : 200;
        const conclWC = (structure.conclusion && structure.conclusion.wordCount) ? structure.conclusion.wordCount : 200;
        const estimatedTotal = introWC + conclWC + 800; // aim for ~1k main content
        const rebuilt = await this.generateKeywordOptimizedStructure(keyword, estimatedTotal, keyword);
        if (rebuilt && Array.isArray(rebuilt.mainSections) && rebuilt.mainSections.length > 0) {
          structure.mainSections = rebuilt.mainSections;
          // Ensure intro/conclusion wc exist
          structure.introduction = structure.introduction || rebuilt.introduction;
          structure.conclusion = structure.conclusion || rebuilt.conclusion;
          console.log('♻️ Rebuilt missing mainSections using generateKeywordOptimizedStructure');
        }
      } catch (e) {
        console.warn('⚠️ Failed to rebuild mainSections:', e && e.message ? e.message : e);
      }
    }

    // Helper: remove model artifacts like fenced code blocks, repeated CTA fragments, or placeholder image labels
    const sanitizeModelArtifacts = (str) => {
      if (!str || typeof str !== 'string') return str;
      let s = String(str);
      // Remove fenced code blocks (```...```)
      s = s.replace(/```[\s\S]*?```/g, '');
      // Remove inline html fences like ```htmlor```text
      s = s.replace(/```\w*\s*/g, '');
      // Remove common placeholder image labels
      s = s.replace(/(?:Feature Image|Solar CAD Design image|Solar CAD Design - Professional Solar Design Services)/gi, '');
      // Remove repeated simple CTAs produced by models (e.g., duplicated 'Ready to optimize' with URL)
      s = s.replace(/(Ready to optimize[\s\S]{0,200}?https?:\/\/[^\s]+)+/gi, (m) => m.split('\n').slice(0,1).join(' '));
      // Remove stray multiple newlines and trim
      s = s.replace(/\n{3,}/g, '\n\n');
      s = s.replace(/\s{2,}/g, ' ').trim();
      return s;
    };

    // Introduction prompt (conversational, blog-style)
    const introPrompt = `METADATA:
{
  "step": "introduction",
  "keyword": "${keyword}",
  "audience": "${targetAudience}",
  "word_target_min": ${Math.floor(structure.introduction.wordCount * 0.9)},
  "word_target_max": ${structure.introduction.wordCount},
  "tone": "conversational, blog-friendly",
  "company": "${companyName}"
}

INSTRUCTIONS:
You are a senior SEO Content Writer. Produce a warm, reader-friendly blog introduction aimed at ${targetAudience} that sounds like it was written by an experienced human.

REQUIREMENTS:
- Include the focus keyword naturally within the first 50 words (prefer natural placement over awkward exact-match forcing).
- Start with a short, relatable anecdote, insight, or single compelling fact to hook the reader.
- Explain why the topic matters in 2 short paragraphs and set expectations for what the reader will learn.
- Use simple, conversational language — this is a blog, not a technical manual.
- Use only the provided links for citations. Do NOT invent links. If you cite, mark inline as [1], [2] and provide a bibliography entry in the JSON.
- Ensure sentences are complete.
${((realLinks && ((realLinks.outboundLinks && realLinks.outboundLinks.length>0) || (realLinks.inboundLinks && realLinks.inboundLinks.length>0))) || (externalReferences && externalReferences.length>0)) ? '- If any links are provided above, include at least one inline citation from them (e.g., [1]) within the introduction.' : ''}

LINKS (use provided or write without links):
${this.formatRealLinksOnly(realLinks.outboundLinks.slice(0,3))}

OUTPUT FORMAT (MANDATORY):
1) HTML content only (using <p>,<strong>,<ul>/<ol> and <a href> where appropriate).

Write between ${Math.floor(structure.introduction.wordCount * 0.9)} and ${structure.introduction.wordCount} words.`;

    const rawIntro = await vertexService.generateContent(introPrompt, companyContext);
  const parsedIntro = this.extractHtmlAndJson(rawIntro.content);
    // If JSON parse failed, parsedIntro.parseError will be true and html contains stripped content
    let adjustedContent = await this.validateAndAdjustWordCount(
      parsedIntro.html,
      structure.introduction.wordCount,
      'introduction'
    );

    adjustedContent = this.removeFakeLinks(adjustedContent, realLinks);

    // sanitize common model artifacts from the intro
    const sanitizedIntro = sanitizeModelArtifacts(adjustedContent);
    if (sanitizedIntro !== adjustedContent) console.log('🧽 Intro sanitized by sanitizeModelArtifacts');

    const enhancedIntroContent = vertexService.enhanceContentWithCompanyInfo(
      sanitizedIntro,
      companyContext,
      keyword
    );

    // store any citations the model returned
    if (parsedIntro.json && Array.isArray(parsedIntro.json.citations)) {
      parsedIntro.json.citations.forEach(c => {
        if (c && c.id && c.url) usedCitations.push({ id: String(c.id), url: c.url, context: c.context || '' });
      });
    }

    blocks.push({
      id: `intro-${blockId++}`,
      type: "paragraph",
      content: enhancedIntroContent,
      contentHtml: enhancedIntroContent,
      wordCount: this.countWordsFromHtml(enhancedIntroContent),
      seoNotes: "Keyword in first 100 words for RankMath compliance"
    });

    // Generate main section blocks in parallel to improve throughput
    const kwEsc = keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const exactRegex = new RegExp(`\\b${kwEsc}\\b`, 'i');

    const sectionTasks = structure.mainSections.map((section, idx) => (async () => {
      // Build prompt for this section
      const sectionPrompt = `**INSTRUCTION:**
You are a senior SEO Content Writer tasked with producing clear, authoritative content that aligns with the introduction, section title, and overall article structure.
**CONTEXT:**
You are working with Gemini model gemini-1.5-flash.
**INPUT DATA:**
- Metadata:
  - Section Title: "${section.heading}"
  - Keyword: "${keyword}"
  - Audience: "${targetAudience}"
  - Word Target Min: ${Math.floor(section.wordCount * 0.9)}
  - Word Target Max: ${section.wordCount}
  - Tone: Conversational, blog-friendly
  - Company: "${companyName}"
- Requirements:
  - Begin with a one-sentence summary of the section.
  - Use <h3> tags for subsections and <ul>/<li> for lists.
  - Include the focus keyword phrase at least once (exact-match required) with sensible variations.
  - Cite relevant facts from API-related links, annotate inline [1], and include mapping in JSON output.
  - Ensure grammatically correct sentences and paragraphs.
  - Add 1–2 connective sentences linking this section to the introduction and following section.
  - Avoid templated phrasing; write in a natural, blog-like style.
  - If provided, include at least one inline citation from the links and record mapping in JSON.
**OUTPUT FORMAT:**
1) HTML content with <h3>, <p>, <ul>/<li> where applicable.
Write between ${Math.floor(section.wordCount * 0.9)} and ${section.wordCount} words.
**ADDITIONAL NOTES:**
- Emphasize natural phrasing over forced exact-match keywords.
- Maintain a conversational tone throughout the content.
- Ensure seamless flow between sections for improved readability.
- Incorporate relevant API-linked citations to enhance credibility.`;
      // Attempt generation with up to 2 retries for exact match and sentence completion
      let attempt = 0;
      let parsedSection = null;
      let rawSection = null;
      let finalHtml = '';
      while (attempt < 3) {
  rawSection = await vertexService.generateContent(sectionPrompt, companyContext);
        parsedSection = this.extractHtmlAndJson(rawSection.content || '');
        let adjusted = await this.validateAndAdjustWordCount(parsedSection.html || '', section.wordCount, 'section');
        adjusted = this.removeFakeLinks(adjusted, realLinks);
  // sanitize section content from model artifacts
  const sanitizedSection = sanitizeModelArtifacts(adjusted);
  if (sanitizedSection !== adjusted) console.log(`🧽 Section ${idx} sanitized by sanitizeModelArtifacts`);
  let enhanced = vertexService.enhanceContentWithCompanyInfo(sanitizedSection, companyContext, keyword);

        // Check for sentence completeness (ends with period, !, or ?) and presence of exact keyword
        const complete = /[\.\!\?]\s*$/.test(enhanced.trim());
        const hasKeyword = exactRegex.test(enhanced);
        if (hasKeyword && complete) { finalHtml = enhanced; break; }

        // Retry with stricter instruction to include exact keyword and finish sentences
        attempt++;
        const retryPrompt = `Please rewrite the following content to ensure all sentences are complete and the exact phrase "${keyword}" appears at least once in the section. Return only the HTML content.\n\nORIGINAL:\n${adjusted}`;
        const retryRes = await vertexService.generateContent(retryPrompt, companyContext);
        const parsedRetry = this.extractHtmlAndJson(retryRes.content || '');
        adjusted = await this.validateAndAdjustWordCount(parsedRetry.html || adjusted, section.wordCount, 'section');
        adjusted = this.removeFakeLinks(adjusted, realLinks);
        enhanced = vertexService.enhanceContentWithCompanyInfo(adjusted, companyContext, keyword);
        if (exactRegex.test(enhanced) && /[\.\!\?]\s*$/.test(enhanced.trim())) { finalHtml = enhanced; break; }

        // if still not good, loop again (max 3 attempts)
        finalHtml = enhanced;
      }

      // collect citations if present
      if (parsedSection && parsedSection.json && Array.isArray(parsedSection.json.citations)) {
        parsedSection.json.citations.forEach(c => {
          if (c && c.id && c.url) usedCitations.push({ id: String(c.id), url: c.url, context: c.context || '' });
        });
      }

      return {
        heading: { id: `h2-temp-${idx}`, type: 'h2', content: section.heading, wordCount: this.countWordsFromHtml(section.heading), seoNotes: 'H2 with focus keyword for content structure' },
        content: { id: `section-temp-${idx}`, type: 'paragraph', content: finalHtml, wordCount: this.countWordsFromHtml(finalHtml), seoNotes: section.keywordRequirement }
      };
    })());

  const sectionResults = await Promise.all(sectionTasks);
    // Append in-order: h2 then section
    for (const resItem of sectionResults) {
      // Store plain heading text in `content` (so editors see clean headings) and keep an HTML version in `contentHtml` for rendering
      const rawHeading = resItem.heading.content || '';
      const headingText = rawHeading.replace(/<[^>]*>/g, '').trim();
      const h2Html = rawHeading.trim().startsWith('<h2') ? rawHeading : `<h2>${headingText}</h2>`;
      blocks.push({ id: `h2-${blockId++}`, type: 'h2', content: headingText, contentHtml: h2Html, wordCount: this.countWordsFromHtml(headingText), seoNotes: resItem.heading.seoNotes });
      const paraHtml = resItem.content.content || '';
      // If paragraph is very long, split into smaller paragraph blocks while preserving lists
      const paraWordCount = this.countWordsFromHtml(paraHtml);
      const maxWordsPerBlock = Math.max(250, Math.round((resItem.content.wordCount || 400) / 1));
      if (paraWordCount > maxWordsPerBlock && !/<ul|<ol|<li/i.test(paraHtml)) {
        const splitBlocks = this.splitHtmlIntoBlocks(paraHtml, maxWordsPerBlock);
        for (const part of splitBlocks) {
          blocks.push({ id: `section-${blockId++}`, type: 'paragraph', content: part,  contentHtml: part, wordCount: this.countWordsFromHtml(part), seoNotes: resItem.content.seoNotes });
        }
      } else {
        blocks.push({ id: `section-${blockId++}`, type: 'paragraph', content: paraHtml, contentHtml: paraHtml, wordCount: paraWordCount, seoNotes: resItem.content.seoNotes });
      }
    }

    // Conclusion block - ENHANCED FOR GENUINE EXPERT CONTENT
    const conclusionPrompt = `METADATA:
{
  "step": "conclusion",
  "keyword": "${keyword}",
  "audience": "${targetAudience}",
  "word_target_min": ${Math.floor(structure.conclusion.wordCount * 0.9)},
  "word_target_max": ${structure.conclusion.wordCount},
  "tone": "decisive, next-steps",
  "company": "${companyName}"
}

INSTRUCTIONS:
You are a senior SEO Content Writer. Write a professional, authoritative conclusion that summarizes the article's top takeaways (take references from the previously generated sections and introduction) and provides a neutral next step for the reader.

REQUIREMENTS:
- Include the exact focus keyword phrase at least once in the conclusion (exact-match required).
- Summarize the three strongest takeaways in concise paragraphs.
- Provide a short, factual mention of ${companyName} if it supports the reader (one sentence max).
- Use provided links only for citations and mark them inline [1].
- Ensure complete sentences and grammatical correctness.
${((realLinks && ((realLinks.outboundLinks && realLinks.outboundLinks.length>0) || (realLinks.inboundLinks && realLinks.inboundLinks.length>0))) || (externalReferences && externalReferences.length>0)) ? '- If any links are provided above, include at least one inline citation from them in the conclusion (annotate as [1]).' : ''}

OUTPUT FORMAT:
1) HTML content only (use <p> and <strong> as needed).
Write between ${Math.floor(structure.conclusion.wordCount * 0.9)} and ${structure.conclusion.wordCount} words.`;

    const rawConclusion = await vertexService.generateContent(conclusionPrompt, companyContext);
    const parsedConclusion = this.extractHtmlAndJson(rawConclusion.content);

    // sanitize conclusion content and then enhance
    const sanitizedConclusion = sanitizeModelArtifacts(parsedConclusion.html || '');
    if (sanitizedConclusion && sanitizedConclusion !== (parsedConclusion.html || '')) console.log('🧽 Conclusion sanitized by sanitizeModelArtifacts');

    let enhancedConclusionContent = vertexService.enhanceContentWithCompanyInfo(
      sanitizedConclusion,
      companyContext,
      keyword
    );

  enhancedConclusionContent = this.removeFakeLinks(enhancedConclusionContent, realLinks);

    // Ensure CTA uses safe fields (no object leaks)
    const safeCompanyName = (companyContext && companyContext.name) ? String(companyContext.name) : 'our team';
    const safeServices = (companyContext && companyContext.servicesOffered) ? String(companyContext.servicesOffered) : 'solar services';
    if (!enhancedConclusionContent.toLowerCase().includes('contact') && !enhancedConclusionContent.toLowerCase().includes('visit')) {
      enhancedConclusionContent += `\n\nReady to optimize your ${keyword} strategy? Contact ${this.escapeHtml(safeCompanyName)} today for expert ${this.escapeHtml(safeServices)}. Visit https://www.wattmonk.com to learn more about our comprehensive solutions.`;
    }

    if (parsedConclusion.json && Array.isArray(parsedConclusion.json.citations)) {
      parsedConclusion.json.citations.forEach(c => {
        if (c && c.id && c.url) usedCitations.push({ id: String(c.id), url: c.url, context: c.context || '' });
      });
    }

    blocks.push({
      id: `conclusion-${blockId++}`,
      type: "paragraph",
      content: enhancedConclusionContent,
      wordCount: this.countWordsFromHtml(enhancedConclusionContent),
      seoNotes: "Conclusion with keyword and CTA"
    });

    // Ensure externalReferences and usedCitations are actually referenced inline.
    const paragraphIndexes = blocks.map((b, idx) => ({ b, idx })).filter(x => x.b && x.b.type === 'paragraph' && typeof x.b.content === 'string');

    // helper: choose best paragraph index for a ref by token overlap using similarityScore
    const findBestParagraphIndexForRef = (ref) => {
      let bestIdx = -1, bestScore = 0;
      const textToMatch = ((ref.title || '') + ' ' + (ref.snippet || '')).toLowerCase();
      for (const p of paragraphIndexes) {
        const txt = (p.b.content || '').toString().toLowerCase();
        const score = this.similarityScore(txt, textToMatch);
        if (score > bestScore) { bestScore = score; bestIdx = p.idx; }
      }
      return bestIdx >= 0 ? bestIdx : (paragraphIndexes.length ? paragraphIndexes[paragraphIndexes.length - 1].idx : -1);
    };

    // Inject any externalReferences (or usedCitations) that are not yet present as anchors into best-fit paragraphs
    const allCandidateRefs = [];
    (externalReferences || []).forEach(r => { if (r && (r.url || r.link)) allCandidateRefs.push({ url: r.url || r.link, text: r.title || r.text || r.headline || r.source || 'Reference', context: r.source || r.provider || r.snippet || '' }); });
    (usedCitations || []).forEach(c => { if (c && c.url) allCandidateRefs.push({ url: c.url, text: c.title || c.text || c.url, context: c.context || '' }); });

    // Sanitize and dedupe candidate refs by url
    const byUrl = {};
    allCandidateRefs.forEach(r => { if (!r || !r.url) return; const key = String(r.url).trim(); if (!byUrl[key]) byUrl[key] = r; });
    let candidates = Object.values(byUrl);

    // Force-inject company homepage and top external references so the final References block is predictable
    try {
      const companyUrl = (companyContext && (companyContext.website || companyContext.url)) ? (companyContext.website || companyContext.url) : null;
      if (companyUrl) {
        const key = String(companyUrl).trim();
        if (!byUrl[key]) {
          candidates.unshift({ url: companyUrl, text: companyName || 'Company Website', context: 'Company' });
          byUrl[key] = { url: companyUrl, text: companyName || 'Company Website', context: 'Company' };
        }
      }
    } catch (e) { /* ignore */ }

    // If there are curated externalReferences, place the top 2 at the front so they're guaranteed to be referenced
    try {
      if (externalReferences && externalReferences.length > 0) {
        const top = externalReferences.slice(0, 2).map(r => ({ url: r.url || r.link, text: r.title || r.text || r.headline || r.source || 'Reference', context: r.source || r.provider || '' }));
        top.reverse().forEach(t => {
          const k = String(t.url).trim();
          if (k && !byUrl[k]) {
            candidates.unshift(t);
            byUrl[k] = t;
          }
        });
      }
    } catch (e) { /* ignore */ }

    // For each candidate, if no block already contains its URL, append a short anchor to the best paragraph
    for (const ref of candidates) {
      const s = this.sanitizeLink(ref);
      if (!s || !s.url) continue;
      const alreadyPresent = blocks.some(b => typeof b.content === 'string' && b.content.includes(s.url));
      if (alreadyPresent) continue;
      const targetIdx = findBestParagraphIndexForRef(ref);
      if (targetIdx >= 0) {
        const shortText = this.createShortLinkText(s.text || s.url, keyword);
        blocks[targetIdx].content = blocks[targetIdx].content + ` <a href="${encodeURI(s.url)}" target="_blank" rel="noopener noreferrer">${this.escapeHtml(shortText)}</a>`;
        blocks[targetIdx].wordCount = this.countWordsFromHtml(blocks[targetIdx].content);
      }
    }

    // Now build the final references list from actual anchors present in blocks
    const discovered = {};
    for (const b of blocks) {
      if (!b || typeof b.content !== 'string') continue;
      // find all anchors
      const re = /<a[^>]*href=["']([^"']+)["'][^>]*>(.*?)<\/a>/gi;
      let m;
      while ((m = re.exec(b.content))) {
        try {
          const url = decodeURI(m[1]);
          const text = m[2].replace(/<[^>]*>/g, '').trim();
          if (!url) continue;
          const key = String(url);
          if (!discovered[key]) discovered[key] = { url: key, text: text || key, context: '' };
        } catch (e) { /* ignore malformed */ }
      }
    }

  const finalRefs = Object.values(discovered);

  // Guarantee company homepage and top externalReferences appear in finalRefs
  try {
    const companyUrl = (companyContext && (companyContext.website || companyContext.url)) ? (companyContext.website || companyContext.url) : null;
    if (companyUrl) {
      const hasCompany = finalRefs.some(r => String(r.url).trim() === String(companyUrl).trim());
      if (!hasCompany) finalRefs.unshift({ url: companyUrl, text: companyName || 'Company Website', context: 'Company' });
    }
    if (externalReferences && externalReferences.length > 0) {
      const topExt = externalReferences.slice(0, 2).map(r => ({ url: r.url || r.link, text: r.title || r.text || r.headline || r.source || 'Reference', context: r.source || r.provider || '' }));
      for (const t of topExt.reverse()) {
        if (!finalRefs.some(r => String(r.url).trim() === String(t.url).trim())) finalRefs.unshift(t);
      }
    }
  } catch (e) { /* ignore */ }

  // Merge model-returned citations with fetched externalReferences for review mapping
  const uniqueRefs = {};
  (externalReferences || []).forEach(r => { if (r && (r.id || r.url)) uniqueRefs[String(r.id || r.url)] = r; });
  (usedCitations || []).forEach(c => { if (c && (c.id || c.url) && !uniqueRefs[String(c.id || c.url)]) uniqueRefs[String(c.id || c.url)] = c; });
  const reviewRefs = Object.values(uniqueRefs);

    // Add the references block using only the actually referenced links
    const referencesContent = this.generateReferencesSection(realLinks, keyword, finalRefs);

    blocks.push({
      id: `references-${blockId++}`,
      type: "references",
      content: referencesContent,
      wordCount: this.countWordsFromHtml(referencesContent),
      seoNotes: "Reference links for credibility and authority"
    });

    // Replace inline citation markers like [1], [R1] with anchor links in paragraph blocks
  if (reviewRefs && reviewRefs.length > 0) {
      // create a map id->ref
      const refMap = {};
      reviewRefs.forEach((r, i) => {
        const id = (r.id || (`R${i+1}`)).toString();
        refMap[id.replace(/^R?/,'')] = r; // key by numeric id if present
        refMap[id] = r; // also key by raw id
      });

      // helper to generate anchor HTML for a ref
      const refAnchorHtml = (r, label) => {
        const s = this.sanitizeLink(r);
        if (!s || !s.url) return label || '';
        return `<a href="${encodeURI(s.url)}" target="_blank" rel="noopener noreferrer">${this.escapeHtml(label || s.text)}</a>`;
      };

      // For each block, replace markers in content and contentHtml
      for (const b of blocks) {
        if (!b || !b.content) continue;
        // operate on HTML content string
        let html = b.contentHtml || b.content || '';
        let replaced = false;
        // Find markers like [R1], [1], [R123]
        html = html.replace(/\[(R?\d+)\]/g, (match, p1) => {
          const ref = refMap[p1] || refMap['R'+p1] || refMap[p1.replace(/^R/,'')];
          if (ref) { replaced = true; return refAnchorHtml(ref, `[${p1.replace(/^R/,'')}]`); }
          return match;
        });

        if (replaced) {
          // Update both contentHtml and plain content (strip tags for editor)
          b.contentHtml = html;
          b.content = html.replace(/<[^>]*>/g, '').trim();
          b.wordCount = this.countWordsFromHtml(b.contentHtml || b.content);
        }
      }

      // Replace any marker-only 'references' blocks (like a block that lists [1][2]) with the full references HTML
      for (let i = blocks.length - 1; i >= 0; i--) {
        const blk = blocks[i];
        if (!blk || blk.type !== 'references') continue;
        const text = (blk.content || '').trim();
        // if block contains only bracket markers and no anchors, replace it
          if (/^\[?\d+\]?([\s\n]*\[?\d+\]?)*$/.test(text) && !/<a [^>]*href=/i.test(text)) {
          // Replace with full references list HTML
          const fullRefsHtml = this.generateReferencesSection(realLinks, keyword);
          blk.content = fullRefsHtml;
          blk.wordCount = this.countWordsFromHtml(fullRefsHtml);
        }
      }

      // push review-only mapped data for editor review (unchanged)
      const mapped = reviewRefs.map(ref => ({ ...ref, anchorText: null, excerpt: null }));
      const combinedHtml = blocks.map(b => (b.contentHtml || b.content || '')).join('\n');
      mapped.forEach(m => {
        try {
          // Look for inline marker like [R1] or [1]
          const idMarker = (m.id || '').toString().replace(/^R?/, '');
          const markers = [`[${m.id}]`, `[R${idMarker}]`, `[${idMarker}]`];
          let foundIndex = -1, foundMarker = null;
          for (const mk of markers) {
            const idx = combinedHtml.indexOf(mk);
            if (idx !== -1) { foundIndex = idx; foundMarker = mk; break; }
          }
          if (foundIndex !== -1) {
            m.anchorText = foundMarker;
            const start = Math.max(0, combinedHtml.lastIndexOf('.', foundIndex) + 1);
            const end = combinedHtml.indexOf('.', foundIndex + 1);
            m.excerpt = combinedHtml.substring(start, end === -1 ? Math.min(start + 300, combinedHtml.length) : end + 1).trim();
          }
        } catch (e) {
          // ignore mapping errors
        }
      });

      blocks.push({
        id: `review-references-${blockId++}`,
        type: "references-review",
        content: mapped,
        seoNotes: "Review-only external references mapping"
      });
    }

      // Deduplicate adjacent or very similar blocks (simple heuristic)
      const deduped = [];
      for (let i = 0; i < blocks.length; i++) {
        const b = blocks[i];
        if (!b) continue;
        const prev = deduped[deduped.length - 1];
        if (prev && prev.type === b.type) {
          const a = (prev.content || '').toString().replace(/<[^>]*>/g, '').trim().toLowerCase();
          const c = (b.content || '').toString().replace(/<[^>]*>/g, '').trim().toLowerCase();
          // If one content includes the other or they are highly similar, skip the latter
          if (!a) { deduped.push(b); continue; }
          if (a === c || a.includes(c) || c.includes(a) || this.similarityScore(a, c) > 0.9) {
            // skip the duplicate block
            continue;
          }
        }
        deduped.push(b);
      }

      return deduped;
  }

  /**
   * Format ONLY real working links for AI prompts - prevents fake link generation
   * @param {Array} links - Array of link objects
   * @returns {string} Formatted real links only
   */
  formatRealLinksOnly(links) {
    if (!links || links.length === 0) {
      return `
1. <a href="https://www.energy.gov/eere/solar/" target="_blank" rel="noopener noreferrer" class="font-medium text-sm text-blue-600 hover:text-blue-800 underline flex items-center gap-1">Department of Energy Solar Research<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link h-3 w-3"><path d="M15 3h6v6"></path><path d="M10 14 21 3"></path><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path></svg></a>
2. <a href="https://www.nrel.gov/solar/" target="_blank" rel="noopener noreferrer" class="font-medium text-sm text-blue-600 hover:text-blue-800 underline flex items-center gap-1">NREL Solar Research<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link h-3 w-3"><path d="M15 3h6v6"></path><path d="M10 14 21 3"></path><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path></svg></a>
3. <a href="https://www.seia.org/solar-industry-research-data" target="_blank" rel="noopener noreferrer" class="font-medium text-sm text-blue-600 hover:text-blue-800 underline flex items-center gap-1">SEIA Industry Data<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link h-3 w-3"><path d="M15 3h6v6"></path><path d="M10 14 21 3"></path><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path></svg></a>`;
    }

    return links.map((link, index) => {
      const s = this.sanitizeLink(link);
      if (!s) return '';
      const href = encodeURI(s.url);
      const text = this.escapeHtml(s.text);
      return `${index + 1}. <a href="${href}" target="_blank" rel="noopener noreferrer" class="font-medium text-sm text-blue-600 hover:text-blue-800 underline flex items-center gap-1">${text}<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link h-3 w-3"><path d="M15 3h6v6"></path><path d="M10 14 21 3"></path><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path></svg></a>`;
    }).join('\n');
  }

  /**
   * Split HTML-free content into multiple paragraph blocks of maxWords each.
   * Preserves existing paragraph boundaries where possible.
   */
  splitHtmlIntoBlocks(htmlContent, maxWords = 300) {
    if (!htmlContent || typeof htmlContent !== 'string') return [];
    // If contains lists or block elements, return as single block to preserve structure
    if (/<ul|<ol|<li|<table|<h[1-6]/i.test(htmlContent)) return [htmlContent];
    // Strip tags for splitting, but keep sentences
    const text = htmlContent.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
    if (!text) return [];
    const words = text.split(' ');
    const parts = [];
    for (let i = 0; i < words.length; i += maxWords) {
      const chunk = words.slice(i, i + maxWords).join(' ');
      parts.push(`<p>${this.escapeHtml(chunk)}</p>`);
    }
    return parts;
  }

  /**
   * Sanitize a link object and return {url, text} or null
   */
  sanitizeLink(link) {
    if (!link) return null;
    const url = (typeof link.url === 'string' && link.url.trim()) ? link.url.trim() : (typeof link.href === 'string' ? link.href.trim() : '');
    const text = (typeof link.text === 'string' && link.text.trim()) ? link.text.trim() : (typeof link.title === 'string' ? link.title.trim() : url || 'Learn More');
    if (!url) return null;
    // Remove any object-to-string leaks and percent-encoded object strings
    const cleanUrl = url.replace(/\[object%20Object\]|\[object Object\]|\(object Object\)/gi, '').trim();
    return { url: cleanUrl, text };
  }

  // Basic HTML escaper for anchor text
  escapeHtml(str) {
    if (!str || typeof str !== 'string') return '';
    return str.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/'/g, '&#39;');
  }

  /**
   * Format links for use in AI prompts with proper styling and external link icons
   * @param {Array} links - Array of link objects
   * @returns {string} Formatted links string
   */
  formatLinksForPrompt(links) {
    if (!links || links.length === 0) {
      return `<a href="https://www.nrel.gov/solar/" target="_blank">NREL Solar Research</a>, <a href="https://www.seia.org/" target="_blank">SEIA Industry Data</a>`;
    }

    return links.map(link => {
      // SIMPLE blue links that open in new tabs
      return `<a href="${link.url}" target="_blank">${link.text}</a>`;
    }).join(', ');
  }

  /**
   * Generate references section with real working links
   * @param {Object} realLinks - Object containing inbound and outbound links
   * @param {string} keyword - Focus keyword
   * @returns {string} HTML references section
   */
  generateReferencesSection(realLinks, keyword, externalReferences = []) {
    // Prefer curated externalReferences (from SERP/Perplexity/linkService) if provided
    const external = (externalReferences || []).filter(r => r && (r.url || r.link)).map(r => ({ url: r.url || r.link || r.source, text: r.title || r.text || r.headline || 'Reference', context: r.source || r.provider || '' }));
    const allLinks = external.length > 0 ? external : [...(realLinks.outboundLinks || []), ...(realLinks.inboundLinks || [])];

    if (!allLinks || allLinks.length === 0) {
      // Fallback to static links only when no dynamic links are available
      return `<h3 style="color: #FBD46F; font-family: Roboto; font-weight: 600;">References</h3>
<ul>
  <li><a href="https://www.nrel.gov/solar/" target="_blank" rel="noopener">NREL Solar Research</a> - National Renewable Energy Laboratory solar technology research and data</li>
  <li><a href="https://www.seia.org/" target="_blank" rel="noopener">SEIA Industry Data</a> - Solar Energy Industries Association market reports and statistics</li>
  <li><a href="https://www.energy.gov/solar/" target="_blank" rel="noopener">DOE Solar Programs</a> - U.S. Department of Energy solar energy initiatives and resources</li>
</ul>`;
    }

    let referencesHTML = `<h3 style="color: #FBD46F; font-family: Roboto; font-weight: 600;">References</h3>\n<ul>\n`;

    // Use up to 6 links
    const linksToUse = allLinks.slice(0, 6);

    linksToUse.forEach(link => {
      const s = this.sanitizeLink(link);
      if (!s) return;
      const href = encodeURI(s.url);
      const linkText = this.escapeHtml(s.text || `${keyword} Resource`);
      const linkContext = this.escapeHtml(link.context || link.source || `Authority content about ${keyword}`);

      referencesHTML += `  <li><a href="${href}" target="_blank" rel="noopener">${linkText}</a> - ${linkContext}</li>\n`;
    });

    referencesHTML += `</ul>`;

    return referencesHTML;
  }

  /**
   * Validate SEO compliance and calculate RankMath score
   */
  validateSEOCompliance(contentBlocks, keyword, metaData) {
    const validation = {
      score: 0,
      maxScore: 100,
      checks: {},
      recommendations: []
    };

    // Combine all content for analysis
    const allContent = contentBlocks
      .filter(block => block.type === 'paragraph')
      .map(block => block.content)
      .join(' ');

    const wordCount = allContent.split(' ').length;
    const keywordCount = this.countKeywordOccurrences(allContent, keyword);
    const keywordDensity = (keywordCount / wordCount) * 100;

    // Exact-match regex for keyword with word boundaries
    const kwEsc = keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const exactRegex = new RegExp(`\\b${kwEsc}\\b`, 'i');

    // Check 1: Focus keyword in title (15 points) - exact match
    if (metaData.h1 && exactRegex.test(metaData.h1)) {
      validation.score += 15;
      validation.checks.keywordInTitle = true;
    } else {
      validation.recommendations.push('Include focus keyword in H1 title as an exact match');
    }

    // Check 2: Focus keyword in meta description (10 points) - exact match within first 80 chars
    if (metaData.metaDescription && exactRegex.test((metaData.metaDescription || '').substring(0, 80))) {
      validation.score += 10;
      validation.checks.keywordInMetaDescription = true;
    } else {
      validation.recommendations.push('Include focus keyword in meta description (exact match) within the opening text');
    }

    // Check 3: Focus keyword in URL (10 points)
    if (metaData.slug && metaData.slug.includes(keyword.toLowerCase().replace(/\s+/g, '-'))) {
      validation.score += 10;
      validation.checks.keywordInURL = true;
    } else {
      validation.recommendations.push('Include focus keyword in URL slug');
    }

    // Check 4: Focus keyword in first 100 words (15 points) - ENHANCED FOR RANKMATH (exact match)
    const first100Words = allContent.split(' ').slice(0, 100).join(' ');
    if (exactRegex.test(first100Words)) {
      validation.score += 15;
      validation.checks.keywordInFirst100Words = true;
    } else {
      validation.recommendations.push('Include focus keyword in first 100 words of content (CRITICAL for RankMath)');
    }

    // Check 5: Focus keyword found in content (10 points)
    if (keywordCount > 0) {
      validation.score += 10;
      validation.checks.keywordInContent = true;
    } else {
      validation.recommendations.push('Include focus keyword in content');
    }

    // Check 6: Content length (10 points)
    if (wordCount >= 1102) {
      validation.score += 10;
      validation.checks.contentLength = true;
    } else {
      validation.recommendations.push(`Increase content length to at least 1102 words (current: ${wordCount})`);
    }

    // Check 7: Title readability (10 points)
    if (metaData.h1 && metaData.h1.length <= 60) {
      validation.score += 10;
      validation.checks.titleReadability = true;
    } else {
      validation.recommendations.push('Keep title under 60 characters');
    }

    // Check 8: Content readability (10 points)
    validation.score += 10; // Assume good readability with our structured approach
    validation.checks.contentReadability = true;

    // Check 9: Meta description length (5 points)
    if (metaData.metaDescription && metaData.metaDescription.length >= 140 && metaData.metaDescription.length <= 160) {
      validation.score += 5;
      validation.checks.metaDescriptionLength = true;
    } else {
      validation.recommendations.push('Meta description should be 140-160 characters');
    }

    // Check 10: Keyword density (5 points)
    if (keywordDensity >= 0.5 && keywordDensity <= 2.5) {
      validation.score += 5;
      validation.checks.keywordDensity = true;
    } else {
      validation.recommendations.push(`Adjust keyword density to 0.5-2.5% (current: ${keywordDensity.toFixed(2)}%)`);
    }

    // Additional RankMath-specific checks

    // Check 11: H2 headings with keyword (5 points)
    const h2Count = (allContent.match(/<h2[^>]*>/gi) || []).length;
    const h2WithKeyword = (allContent.match(new RegExp(`<h2[^>]*>.*${keyword}.*</h2>`, 'gi')) || []).length;
    if (h2WithKeyword >= 1) {
      validation.score += 5;
      validation.checks.keywordInH2 = true;
    } else {
      validation.recommendations.push('Include focus keyword in at least one H2 heading');
    }

    // Check 11b: Detect duplicate or generic H2s (penalty if many duplicates)
    const h2Texts = (allContent.match(/<h2[^>]*>(.*?)<\/h2>/gi) || []).map(t => t.replace(/<[^>]*>/g, '').trim().toLowerCase());
    const uniqueH2 = new Set(h2Texts);
    if (h2Texts.length > 1 && uniqueH2.size < Math.max(1, Math.floor(h2Texts.length / 2))) {
      validation.recommendations.push('Avoid repeating identical or generic H2 headings; make each H2 topic-specific');
    }

    // Check 11c: Encourage H3 usage for depth when sections are long
    const h3Count = (allContent.match(/<h3[^>]*>/gi) || []).length;
    if (h3Count === 0 && h2Count > 2) {
      validation.recommendations.push('Use H3 subheadings within long sections to improve structure and readability');
    }

    // Check 12: Internal links (5 points)
    const internalLinks = (allContent.match(/<a[^>]*href[^>]*>/gi) || []).length;
    if (internalLinks >= 2) {
      validation.score += 5;
      validation.checks.internalLinks = true;
    } else {
      validation.recommendations.push('Add at least 2 internal links for better SEO');
    }

    // Check 13: Image alt text with keyword (5 points)
    const imageAltWithKeyword = (allContent.match(new RegExp(`alt="[^"]*${keyword}[^"]*"`, 'gi')) || []).length;
    if (imageAltWithKeyword >= 1) {
      validation.score += 5;
      validation.checks.keywordInImageAlt = true;
    } else {
      validation.recommendations.push('Include focus keyword in at least one image alt text');
    }

    validation.keywordDensity = keywordDensity;
    validation.wordCount = wordCount;
    validation.keywordCount = keywordCount;
    validation.h2Count = h2Count;
    validation.h2WithKeyword = h2WithKeyword;

    // Determine overall grade - ENHANCED FOR RANKMATH (Target: 85-88/100)
    if (validation.score >= 85) {
      validation.grade = 'A';
      validation.status = 'Excellent - RankMath Optimized';
      validation.color = 'green';
    } else if (validation.score >= 75) {
      validation.grade = 'B+';
      validation.status = 'Good - Near RankMath Target';
      validation.color = 'orange';
    } else if (validation.score >= 65) {
      validation.grade = 'B';
      validation.status = 'Good - Needs RankMath Optimization';
      validation.color = 'orange';
    } else {
      validation.grade = 'C';
      validation.status = 'Needs Major RankMath Improvements';
      validation.color = 'red';
    }

    return validation;
  }

  /**
   * Count keyword occurrences in content
   */
  countKeywordOccurrences(content, keyword) {
    const regex = new RegExp(keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
    const matches = content.match(regex);
    return matches ? matches.length : 0;
  }

  /**
   * Generate SEO-friendly slug
   */
  generateSEOSlug(text) {
    return text
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
      .substring(0, 50);
  }

  /**
   * Regenerate individual content block with real links
   */
  async regenerateContentBlock(blockType, keyword, companyName, companyContext = {}, wordCount = 400, customPrompt = null, sectionTitle = null) {
    console.log(`🔄 Regenerating ${blockType} block with SEO optimization and real links`);

    // Get real links for the keyword
    const linkService = require('./linkService');
    let realLinks = { inboundLinks: [], outboundLinks: [] };

    try {
      realLinks = await linkService.generateInboundOutboundLinks(keyword, companyName);
      console.log(`🔗 Retrieved ${realLinks.inboundLinks.length} inbound and ${realLinks.outboundLinks.length} outbound links for regeneration`);
    } catch (error) {
      console.warn('⚠️ LinkService not available for regeneration, using fallback links');
    }

    let prompt = '';
    let seoNotes = '';

    if (blockType === 'introduction') {
      prompt = customPrompt || `Write a compelling, expert-level introduction about "${keyword}" for solar industry professionals.

CRITICAL INSTRUCTIONS:
- ABSOLUTE WORD LIMIT: MAXIMUM ${wordCount} words - STOP IMMEDIATELY when you reach this limit
- Count every single word as you write and STOP at word ${wordCount}
- Include "${keyword}" within the first 50 words (CRITICAL for RankMath)
- Write with deep technical expertise and industry knowledge
- Use specific data, statistics, and real-world examples

LINK REQUIREMENTS - ABSOLUTELY CRITICAL - NO EXCEPTIONS:
- FORBIDDEN: Creating ANY fake, placeholder, localhost, or made-up links
- FORBIDDEN: Using [object Object] or any placeholder text
- FORBIDDEN: Creating links that don't exist in the provided list
- MANDATORY: ONLY copy-paste these EXACT working links if you need references:

${this.formatRealLinksOnly(realLinks.outboundLinks.slice(0, 2))}

- If you want to reference something, copy-paste ONE of the above links EXACTLY as shown
- If none of the provided links are suitable, write content WITHOUT any links
- DO NOT modify the URLs or link text in any way

CONTENT STYLE - EXPERT LEVEL:
- Start with a compelling industry statistic or recent development
- Reference specific technical standards, regulations, or industry benchmarks
- Include actual performance metrics and efficiency numbers
- Mention real challenges that solar professionals face
- Use technical terminology appropriately
- Show deep understanding of the solar industry

Company: ${companyName}
Services: ${companyContext.servicesOffered || 'Solar Design, Engineering, Permitting, Installation Support'}

Write ONLY the introduction content - no headings, no other sections.`;
      seoNotes = "Keyword in first 50 words for RankMath compliance";

    } else if (blockType === 'section') {
      // CRITICAL: Generate content that specifically addresses the section title
      const actualSectionTitle = sectionTitle || `${keyword} Professional Guide`;
      const sectionFocus = this.getSectionFocus(actualSectionTitle, keyword);

      prompt = customPrompt || `Write a detailed section specifically about "${actualSectionTitle}" related to "${keyword}" for solar industry professionals.

CRITICAL SECTION FOCUS:
- This section MUST specifically address: "${actualSectionTitle}"
- Content must be directly relevant to the section heading
- DO NOT repeat generic information about "${keyword}"
- Focus on the specific aspect mentioned in the section title

SPECIFIC CONTENT REQUIREMENTS FOR "${actualSectionTitle}":
${sectionFocus}

CRITICAL INSTRUCTIONS:
- ABSOLUTE WORD LIMIT: MAXIMUM ${wordCount} words - STOP IMMEDIATELY when you reach this limit
- Count every single word as you write and STOP at word ${wordCount}
- Include "${keyword}" naturally 2-3 times throughout the content
- Write with deep technical expertise and industry knowledge
- Use specific data, statistics, and real-world examples

CONTENT REQUIREMENTS:
- NO LINKS of any kind - focus purely on valuable content
- NO HTML tags, NO markdown, NO special formatting
- Write in clean, readable paragraphs
- Make content engaging and valuable for professionals

CONTENT QUALITY REQUIREMENTS:
- Write substantial, detailed content with minimum 3-4 paragraphs
- Each paragraph should be 100-150 words minimum
- Include specific data, statistics, real examples, and case studies
- Use professional but engaging language that demonstrates expertise
- Provide actionable insights and practical advice professionals can implement
- Reference real industry standards, codes, and best practices
- Include specific equipment models, efficiency ratings, and technical specifications
- Add real-world project examples with actual numbers and results

Company: ${companyName}
Services: ${companyContext.servicesOffered || 'Solar Design, Engineering, Permitting, Installation Support'}

Write ONLY the section content - no headings. Focus specifically on "${actualSectionTitle}".`;
      seoNotes = `Technical content specifically about ${actualSectionTitle} with keyword integration`;

    } else if (blockType === 'conclusion') {
      prompt = customPrompt || `Write a professional conclusion for the "${keyword}" article that demonstrates industry expertise.

CRITICAL INSTRUCTIONS:
- ABSOLUTE WORD LIMIT: MAXIMUM ${wordCount} words - STOP IMMEDIATELY when you reach this limit
- Count every single word as you write and STOP at word ${wordCount}
- Include "${keyword}" naturally in the summary
- Focus on technical insights and industry implications

LINK REQUIREMENTS - ABSOLUTELY CRITICAL - NO EXCEPTIONS:
- FORBIDDEN: Creating ANY fake, placeholder, localhost, or made-up links
- FORBIDDEN: Using [object Object] or any placeholder text
- FORBIDDEN: Creating links that don't exist in the provided list
- MANDATORY: ONLY copy-paste these EXACT working links if you need references:

${this.formatRealLinksOnly(realLinks.inboundLinks.slice(0, 1))}

- If you want to reference something, copy-paste ONE of the above links EXACTLY as shown
- If none of the provided links are suitable, write content WITHOUT any links
- DO NOT modify the URLs or link text in any way

CONCLUSION STYLE - EXPERT LEVEL:
- Summarize key technical insights and industry implications
- Reference future trends or emerging technologies
- Highlight measurable benefits and performance improvements
- Discuss industry best practices and standards
- Provide actionable technical recommendations

Company: ${companyName}
Services: ${companyContext.servicesOffered || 'Solar Design, Engineering, Permitting, Installation Support'}

Write ONLY the conclusion content - no headings.`;
      seoNotes = "Conclusion with keyword and technical insights";

    } else {
      // Generic content block
      prompt = customPrompt || `Write expert content about "${keyword}" for solar industry professionals.

CRITICAL INSTRUCTIONS:
- ABSOLUTE WORD LIMIT: MAXIMUM ${wordCount} words - STOP IMMEDIATELY when you reach this limit
- Count every single word as you write and STOP at word ${wordCount}
- Include "${keyword}" naturally in the content
- Write with deep technical expertise and industry knowledge

LINK REQUIREMENTS - ABSOLUTELY CRITICAL - NO EXCEPTIONS:
- FORBIDDEN: Creating ANY fake, placeholder, localhost, or made-up links
- FORBIDDEN: Using [object Object] or any placeholder text
- FORBIDDEN: Creating links that don't exist in the provided list
- MANDATORY: ONLY copy-paste these EXACT working links if you need references:

${this.formatRealLinksOnly(realLinks.outboundLinks.slice(0, 2))}

- If you want to reference something, copy-paste ONE of the above links EXACTLY as shown
- If none of the provided links are suitable, write content WITHOUT any links
- DO NOT modify the URLs or link text in any way

Company: ${companyName}
Services: ${companyContext.servicesOffered || 'Solar Design, Engineering, Permitting, Installation Support'}`;
      seoNotes = "Expert content with keyword integration";
    }

    // Generate content using Vertex AI
    const vertexService = require('./geminiService');
    const result = await vertexService.generateContent(prompt, companyContext);

    let content = result.content;

    // Validate and adjust word count
  content = await this.validateAndAdjustWordCount(content, wordCount, blockType);

    // CRITICAL: Remove any fake links that might have slipped through
    content = this.removeFakeLinks(content, realLinks);

    // ENSURE OUTBOUND LINKS: Add external links to ALL content blocks for better SEO
    if (realLinks.outboundLinks && realLinks.outboundLinks.length > 0) {
      // Add 1-2 contextual outbound links within the content
      const relevantLinks = realLinks.outboundLinks.slice(0, 2);
      relevantLinks.forEach((link, index) => {
        if (index === 0 && link.url && link.text) {
          // Add first link in the middle of content with COMPLETE link tags
          const sentences = content.split('. ');
          if (sentences.length > 3) {
            const midPoint = Math.floor(sentences.length / 2);
            // Create SHORT link text (max 3-4 words)
            const shortLinkText = this.createShortLinkText(link.text, keyword);
            // SIMPLE blue link that opens in new tab
            if (link.url.startsWith('http')) {
              sentences[midPoint] += ` For more information, see <a href="${link.url}" target="_blank">${shortLinkText}</a>.`;
              content = sentences.join('. ');
            }
          }
        }
      });

      // Add simple links at the end if this is conclusion
      if (blockType === 'conclusion' && realLinks.outboundLinks && realLinks.outboundLinks.length > 0) {
        content += this.addOutboundLinksSection(realLinks.outboundLinks, keyword);
      }
    }

    // Clean up content formatting for WordPress
    content = content.replace(/\n{3,}/g, '\n\n'); // Remove excessive line breaks
    content = content.replace(/\s{2,}/g, ' '); // Remove excessive spaces

    // Remove image placeholders that appear as text
    content = content.replace(/\[Image:[^\]]+\]/g, '');

    // Ensure proper paragraph breaks for readability
    content = content.replace(/\. ([A-Z])/g, '.\n\n$1');

    content = content.trim();

    return {
      content: content,
      wordCount: content.split(' ').filter(w => w.trim().length > 0).length,
      seoNotes: seoNotes,
      seoOptimized: true,
      source: 'seo-optimization-service'
    };
  }

  /**
   * Get specific content focus based on section title
   */
  getSectionFocus(sectionTitle, keyword) {
    const title = sectionTitle.toLowerCase();

    if (title.includes('what is') || title.includes('technical overview')) {
      return `- Define what ${keyword} actually means and how it works
- Explain the technical components and processes involved
- Describe the science/technology behind it
- Include technical specifications and parameters
- Explain how it differs from alternatives`;
    }

    if (title.includes('benefits') || title.includes('advantages')) {
      return `- List specific measurable benefits (percentages, cost savings, efficiency gains)
- Explain WHY these benefits occur (technical reasons)
- Compare benefits vs alternatives
- Include real-world performance data
- Discuss long-term advantages`;
    }

    if (title.includes('installation') || title.includes('process') || title.includes('requirements')) {
      return `- Step-by-step installation process
- Required tools, equipment, and materials
- Technical requirements and specifications
- Safety considerations and codes compliance
- Common installation challenges and solutions`;
    }

    if (title.includes('maintenance') || title.includes('performance') || title.includes('long-term')) {
      return `- Specific maintenance procedures and schedules
- Performance monitoring and optimization
- Common issues and troubleshooting
- Long-term performance expectations
- Maintenance costs and requirements`;
    }

    if (title.includes('cost') || title.includes('pricing') || title.includes('roi')) {
      return `- Detailed cost breakdown and pricing factors
- ROI calculations and payback periods
- Cost comparison with alternatives
- Financing options and incentives
- Total cost of ownership analysis`;
    }

    if (title.includes('types') || title.includes('options') || title.includes('comparison')) {
      return `- Different types/options available
- Technical specifications for each type
- Pros and cons comparison
- Best use cases for each option
- Selection criteria and recommendations`;
    }

    // Default for any other section
    return `- Focus specifically on the aspect mentioned in "${sectionTitle}"
- Provide detailed, technical information about this specific topic
- Include practical examples and real-world applications
- Avoid repeating general information about ${keyword}`;
  }

  /**
   * Remove fake links and replace with real ones or remove entirely
   */
  removeFakeLinks(content, realLinks) {
    if (!content) return content;

    // Remove any localhost links
    content = content.replace(/href="[^"]*localhost[^"]*"/g, '');

    // Remove any [object Object] references
    content = content.replace(/\[object\s+Object\]/g, '');
    content = content.replace(/\{\s*name:\s*[^}]*\}/g, ''); // Remove object literals

    // ENHANCED: Remove database object references that leak into content
    content = content.replace(/\{\s*name:\s*'[^']*',\s*description:\s*'[^']*',\s*_id:\s*new ObjectId\([^)]*\)\s*\}/g, '');
    content = content.replace(/new ObjectId\([^)]*\)/g, '');
    content = content.replace(/\{\s*\n\s*name:\s*'[^']*',[\s\S]*?\}/g, ''); // Multi-line object literals

    // Remove any incomplete or broken link tags
    content = content.replace(/<a[^>]*href="[^"]*\[object[^"]*"[^>]*>.*?<\/a>/g, '');
    content = content.replace(/<a[^>]*href=""[^>]*>.*?<\/a>/g, '');

    // CRITICAL: Remove incomplete <a href=" tags without closing
    content = content.replace(/<a href="[^"]*"?\s*$/g, '');
    content = content.replace(/<a href="\s*$/g, '');
    content = content.replace(/<a href="$/g, '');
    content = content.replace(/href="\s*$/g, '');

    // Remove malformed links with embedded SVG or class attributes in URLs
    content = content.replace(/href="[^"]*class="[^"]*"/g, 'href="#"');
    content = content.replace(/href="[^"]*svg[^"]*"/g, 'href="#"');
    content = content.replace(/href="[^"]*lucide[^"]*"/g, 'href="#"');

    // Remove broken link fragments that appear as text
    content = content.replace(/https?:\/\/[^"\s]*" target="_blank" rel="noopener noreferrer"[^.]*\./g, '');
    content = content.replace(/target="_blank" rel="noopener noreferrer"[^<]*</g, '<');

    // Remove any links that contain debug, undefined, or placeholder text
    content = content.replace(/<a[^>]*href="[^"]*debug[^"]*"[^>]*>.*?<\/a>/g, '');
    content = content.replace(/<a[^>]*href="[^"]*undefined[^"]*"[^>]*>.*?<\/a>/g, '');
    content = content.replace(/<a[^>]*href="[^"]*placeholder[^"]*"[^>]*>.*?<\/a>/g, '');

    // Clean up broken link structures with embedded HTML
    content = content.replace(/href="[^"]*" target="_blank" rel="noopener noreferrer" class="[^"]*">/g, '" target="_blank" rel="noopener noreferrer">');

    // Remove SVG code that got embedded in content
    content = content.replace(/<svg[^>]*>.*?<\/svg>/g, '');
    content = content.replace(/http:\/\/www\.w3\.org\/2000\/svg[^"]*"/g, '"');

    // ENHANCED: Remove broken SVG elements and malformed links
    content = content.replace(/<a href="w3\.org\/2000\/svg"[^>]*>.*?<\/a>/g, '');
    content = content.replace(/w3\.org\/2000\/svg[^>]*>/g, '');
    content = content.replace(/stroke-width="[^"]*"\s*stroke-linecap="[^"]*"[^>]*>/g, '');
    content = content.replace(/class="lucide lucide-external-link[^"]*"[^>]*>/g, '');
    content = content.replace(/viewBox="[^"]*"\s*fill="[^"]*"[^>]*>/g, '');

    // FINAL CLEANUP: Remove any remaining broken elements
    content = content.replace(/<a href="<path[^>]*>/g, ''); // Remove broken path links
    content = content.replace(/<path[^>]*>/g, ''); // Remove orphaned path elements
    content = content.replace(/\{\s*\n\s*name:\s*'[^']*',[\s\S]*?\n\}/g, ''); // Multi-line objects at end
    content = content.replace(/Contact WattMonk today for expert \{[\s\S]*?\}/g, 'Contact WattMonk today for expert solar services'); // Fix broken CTA

    // AGGRESSIVE: Remove any remaining database object patterns
    content = content.replace(/\{\s*name:\s*'[^']*',[\s\S]*?\}/g, ''); // Any remaining object patterns
    content = content.replace(/new ObjectId\([^)]*\)/g, ''); // Any remaining ObjectId references
    content = content.replace(/\n\s*\}\s*\.\s*Visit/g, '. Visit'); // Fix broken endings

    // Clean up any orphaned link text that might be left
    content = content.replace(/\(\s*\)/g, ''); // Remove empty parentheses
    content = content.replace(/\s+/g, ' '); // Clean up extra spaces
    content = content.trim();

    console.log('🧹 Cleaned fake links and malformed HTML from content');
    return content;
  }

  /**
   * Create short, clean link text (max 3-4 words)
   */
  createShortLinkText(originalText, keyword) {
    if (!originalText) return 'Learn More';

    // Remove common prefixes and suffixes
    let shortText = originalText
      .replace(/^(Read more about|Learn more about|More information on|Details about|Guide to|Information on)/i, '')
      .replace(/(here|now|today|guide|information|details)$/i, '')
      .trim();

    // If still too long, extract key words
    const words = shortText.split(' ');
    if (words.length > 4) {
      // Try to keep keyword-related words
      const keywordWords = words.filter(word =>
        word.toLowerCase().includes(keyword.toLowerCase().split(' ')[0]) ||
        ['solar', 'energy', 'panel', 'installation', 'system', 'guide', 'tips'].includes(word.toLowerCase())
      );

      if (keywordWords.length > 0 && keywordWords.length <= 4) {
        shortText = keywordWords.slice(0, 3).join(' ');
      } else {
        // Take first 3 meaningful words
        shortText = words.slice(0, 3).join(' ');
      }
    }

    // Fallback to generic text if still too long or empty
    if (shortText.length > 30 || shortText.length < 3) {
      return 'Learn More';
    }

    return shortText;
  }

  /**
   * Add outbound links section for SEO
   */
  addOutboundLinksSection(outboundLinks, keyword) {
    if (!outboundLinks || outboundLinks.length === 0) return '';

    // Simple links section (like the original project)
    let linksSection = '\n\n';

    outboundLinks.slice(0, 3).forEach(link => {
      const s = this.sanitizeLink(link);
      if (!s) return;
      const shortText = this.escapeHtml(this.createShortLinkText(s.text, keyword));
      linksSection += `<a href="${encodeURI(s.url)}" target="_blank">${shortText}</a>\n`;
    });

    console.log(`🔗 Added simple links section with ${outboundLinks.length} links`);
    return linksSection;
  }

  /**
   * Validate and adjust word count to match target
   */
  async validateAndAdjustWordCount(content, wordCount, blockType) {
    if (!content || !wordCount) {
      return content;
    }

    // Remove HTML tags for word counting
    const textContent = content.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
    const words = textContent.split(' ').filter(word => word.length > 0);
    const currentWordCount = words.length;

    console.log(`📊 ${blockType} word count: ${currentWordCount}/${wordCount} words`);

    const padding = 200; // allow ±200 words padding
    const minAcceptable = Math.max(50, wordCount - padding);
    const maxAcceptable = wordCount + padding;

    if (currentWordCount >= minAcceptable && currentWordCount <= maxAcceptable) {
      console.log(`✅ ${blockType} word count within acceptable range: ${currentWordCount}/${wordCount}`);
      return content; // within acceptable padding
    }

    // If content is too long, attempt a regeneration request to the LLM to trim while preserving sentences
    if (currentWordCount > maxAcceptable) {
      console.log(`✂️ Content too long (${currentWordCount}), requesting soft trim to ${wordCount} words`);
      // Ask the model to trim gracefully (preserve sentence boundaries)
      try {
        const trimPrompt = `Please shorten the following content to approximately ${wordCount} words while preserving full sentences, paragraph structure, and technical accuracy. Return only the HTML content.\n\nCONTENT:\n${content}`;
        const vertex = require('./geminiService');
        const trimmed = await vertex.generateContent(trimPrompt, {});
        const trimmedText = (trimmed && trimmed.content) ? trimmed.content : content;
        // Final soft cleanup
        const finalText = trimmedText.trim();
        console.log(`✂️ Soft trim completed. New length approx: ${finalText.replace(/<[^>]*>/g, ' ').split(' ').filter(w => w.length>0).length}`);
        return finalText;
      } catch (err) {
        console.warn('⚠️ Soft trim failed, performing safe truncate preserving sentences');
        // Fallback: truncate at sentence boundary
        const sentences = content.match(/[^\.\!\?]+[\.\!\?]+/g) || [content];
        let acc = '';
        let count = 0;
        for (const s of sentences) {
          const w = s.replace(/<[^>]*>/g, ' ').trim().split(' ').filter(w=>w.length>0).length;
          if (count + w > wordCount) break;
          acc += s;
          count += w;
        }
        if (count === 0) {
          // As last resort, hard slice words but keep a period
          const wordsSlice = words.slice(0, wordCount);
          let exactText = wordsSlice.join(' ');
          if (!/[\.\!\?]$/.test(exactText)) exactText += '.';
          return `<p>${exactText}</p>`;
        }
        return `<p>${acc.trim()}</p>`;
      }
    }

    // If content is too short, attempt to regenerate or expand
    if (currentWordCount < minAcceptable) {
      console.log(`🔄 Content too short (${currentWordCount}), requesting expansion to reach ${wordCount}`);
      try {
        const expandPrompt = `Please expand the following content to approximately ${wordCount} words while preserving technical accuracy and sentence integrity. Return only the HTML content.\n\nCONTENT:\n${content}`;
        const vertex = require('./geminiService');
        const expanded = await vertex.generateContent(expandPrompt, {});
        const expandedText = (expanded && expanded.content) ? expanded.content : content;
        console.log(`🔄 Expansion completed. New length approx: ${expandedText.replace(/<[^>]*>/g, ' ').split(' ').filter(w => w.length>0).length}`);
        return expandedText;
      } catch (err) {
        console.warn('⚠️ Expansion failed, returning original content for manual review');
        return content;
      }
    }

    return content;
  }

  /**
   * Validate a list of reference objects' URLs by performing HEAD requests and returning only valid ones
   */
  async validateUrls(references) {
    if (!references || !Array.isArray(references)) return [];
    const validated = [];
    const fetchFn = (typeof fetch !== 'undefined') ? fetch : (url, opts) => {
      // dynamic require to avoid module errors if not installed
      try {
        const nodeFetch = require('node-fetch');
        return nodeFetch(url, opts);
      } catch (e) {
        return Promise.reject(e);
      }
    };

    for (const ref of references) {
      try {
        const res = await fetchFn(ref.url, { method: 'HEAD', redirect: 'follow' });
        if (res && (res.status >= 200 && res.status < 400)) {
          validated.push(ref);
          continue;
        }
      } catch (err) {
        // ignore and try GET
      }

      try {
        const r2 = await fetchFn(ref.url, { method: 'GET', redirect: 'follow' });
        if (r2 && (r2.status >= 200 && r2.status < 400)) validated.push(ref);
      } catch (err) {
        // drop the ref
      }
    }
    return validated;
  }

  /**
   * Extract trailing JSON (or
```json

```
fenced block) from model output and return { html, json, parseError }
   */
  extractHtmlAndJson(raw) {
    if (!raw || typeof raw !== 'string') return { html: '', json: null, parseError: true };

  // First, look for fenced```json
const fencedMatch = raw.match(/```json\s*([\s\S]*?)\s*```\s*$/i);
    if (fencedMatch && fencedMatch[1]) {
      const jsonText = fencedMatch[1].trim();
      const html = raw.replace(fencedMatch[0], '').trim();
      try {
        const parsed = JSON.parse(jsonText);
        return { html: html, json: parsed, parseError: false };
      } catch (e) {
        return { html: html, json: null, parseError: true };
      }
    }

    // Next, attempt to find last JSON object at the end
    // Find the last '{' that could start a JSON object
    const lastBrace = raw.lastIndexOf('{');
    if (lastBrace > -1) {
      const possibleJson = raw.substring(lastBrace);
      try {
        const parsed = JSON.parse(possibleJson);
        const html = raw.substring(0, lastBrace).trim();
        return { html: html, json: parsed, parseError: false };
      } catch (e) {
        // fallback: try to locate newline + '{'
        const nlBrace = raw.lastIndexOf('\n{');
        if (nlBrace > -1) {
          const possibleJson2 = raw.substring(nlBrace + 1);
          try {
            const parsed2 = JSON.parse(possibleJson2);
            const html = raw.substring(0, nlBrace + 1).trim();
            return { html: html, json: parsed2, parseError: false };
          } catch (e2) {
            // If JSON parse fails, attempt to heuristically strip a trailing {...} fragment
            const closing = raw.lastIndexOf('}');
            if (closing > lastBrace) {
              // attempt to find the matching opening brace before the closing
              const openIdx = raw.lastIndexOf('{', closing);
              if (openIdx > -1 && openIdx >= lastBrace) {
                const html = raw.substring(0, openIdx).trim();
                return { html: html, json: null, parseError: true };
              }
            }
            // otherwise give up and continue
          }
        }
      }
    }

    // No JSON found; return raw as HTML
    return { html: raw.trim(), json: null, parseError: false };
  }

  // Helper: count words from HTML by stripping tags and counting tokens
  countWordsFromHtml(html) {
    if (!html || typeof html !== 'string') return 0;
    const text = html.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
    if (!text) return 0;
    return text.split(' ').filter(w => w.length > 0).length;
  }

  /**
   * Simple similarity score between two strings using Jaccard on word tokens.
   * Returns 0..1 where 1 is identical.
   */
  similarityScore(a, b) {
    if (!a || !b) return 0;
    const aTokens = new Set(a.split(/\s+/).map(t => t.replace(/[^a-z0-9]/gi, '').toLowerCase()).filter(Boolean));
    const bTokens = new Set(b.split(/\s+/).map(t => t.replace(/[^a-z0-9]/gi, '').toLowerCase()).filter(Boolean));
    if (aTokens.size === 0 || bTokens.size === 0) return 0;
    const intersection = [...aTokens].filter(x => bTokens.has(x)).length;
    const union = new Set([...aTokens, ...bTokens]).size;
    return union === 0 ? 0 : (intersection / union);
  }

  /**
   * Aggressively remove any JSON-like fragments that may appear inside HTML content.
   * Keeps code blocks and legitimate JSON examples if they are inside <pre> or <code> tags.
   */
  removeEmbeddedJsonFragments(content) {
    if (!content || typeof content !== 'string') return content;

    // Preserve JSON/code blocks inside <pre> or <code>
    const preserved = [];
    let tmp = content.replace(/<pre[\s\S]*?<\/pre>/gi, (m) => { preserved.push(m); return `___PRESERVE_BLOCK_${preserved.length - 1}___`; });
    tmp = tmp.replace(/<code[\s\S]*?<\/code>/gi, (m) => { preserved.push(m); return `___PRESERVE_BLOCK_${preserved.length - 1}___`; });

    // Remove fenced JSON blocks
    tmp = tmp.replace(/```json[\s\S]*?```/gi, '');

    // Remove standalone JSON objects that appear on their own lines (heuristic)
    tmp = tmp.replace(/\n?\s*\{[\s\S]{20,2000}\}\s*\n?/g, '\n');

    // Remove isolated JSON arrays
    tmp = tmp.replace(/\n?\s*\[[\s\S]{20,2000}\]\s*\n?/g, '\n');

    // Remove any trailing {...} fragments that are not inside tags
    tmp = tmp.replace(/>\s*\{[^>]*\}\s*</g, '><');

    // Restore preserved blocks
    tmp = tmp.replace(/___PRESERVE_BLOCK_(\d+)___/g, (_, idx) => preserved[Number(idx)] || '');

    // Final sanitize: remove obvious JSON-like sequences (key: value) outside tags
    tmp = tmp.replace(/\b"?[a-zA-Z_][a-zA-Z0-9_\-]*"?\s*:\s*(?:\".*?\"|\d+|true|false|null)\s*(,?)/g, '');

    return tmp.trim();
  }

  /**
   * Insert contextual links into paragraph blocks using validated realLinks.
   * Preference: insert external outbound links in mid-paragraph and inbound internal links near the end.
   */
  async insertLinksIntoParagraphs(blocks, realLinks) {
    if (!Array.isArray(blocks) || !realLinks) return;
    const outbound = (realLinks.outboundLinks || []).filter(l => l.url && l.text).slice(0, 4);
    const inbound = (realLinks.inboundLinks || []).filter(l => l.url && l.text).slice(0, 4);

    // Deterministic strategy:
    // - Use outbound links for the earliest paragraph blocks (left-to-right)
    // - Use inbound links for the latest paragraph blocks (right-to-left)
    const paragraphIndexes = blocks.map((blk, idx) => ({ blk, idx })).filter(x => x.blk && x.blk.type === 'paragraph' && x.blk.content && !/<a [^>]*href=/i.test(x.blk.content));

    // Insert outbound links into the first N paragraphs
    for (let i = 0; i < outbound.length && i < paragraphIndexes.length; i++) {
      const { blk } = paragraphIndexes[i];
      const link = outbound[i];
      if (!link || !link.url) continue;
      const anchorText = link.text || this.createShortLinkText(link.url, '');
      // Insert after first sentence if possible
      const split = blk.content.split(/([\.\!\?]\s+)/);
      if (split.length >= 3) {
        // Rebuild with anchor after first sentence separator
  const s = this.sanitizeLink(link);
  if (s && s.url) split[0] = split[0] + ` <a href="${encodeURI(s.url)}" target="_blank" rel="noopener" style="color:#1155cc;text-decoration:underline;">${this.escapeHtml(anchorText)}</a>`;
        blk.content = split.join('');
      } else {
  const s2 = this.sanitizeLink(link);
  if (s2 && s2.url) blk.content = `${blk.content} <a href="${encodeURI(s2.url)}" target="_blank" rel="noopener" style="color:#1155cc;text-decoration:underline;">${this.escapeHtml(anchorText)}</a>`;
      }
      blk.wordCount = this.countWordsFromHtml(blk.content);
    }

    // Insert inbound links into the last M paragraphs
    for (let j = 0; j < inbound.length && j < paragraphIndexes.length; j++) {
      const target = paragraphIndexes[paragraphIndexes.length - 1 - j];
      if (!target) continue;
      const blk = target.blk;
      const link = inbound[j];
      if (!link || !link.url) continue;
      const anchorText = link.text || this.createShortLinkText(link.url, '');
      // Append at the end as internal link (target _self)
  const s3 = this.sanitizeLink(link);
  if (s3 && s3.url) blk.content = `${blk.content} <a href="${encodeURI(s3.url)}" target="_blank" rel="noopener noreferrer" style="color:#1155cc;text-decoration:underline;">${this.escapeHtml(anchorText)}</a>`;
      blk.wordCount = this.countWordsFromHtml(blk.content);
    }
  }

  /**
   * Fix meta description to be within 140-160 characters and include company name if helpful
   */
  fixMetaDescription(desc, companyName) {
    if (!desc || typeof desc !== 'string') desc = `${companyName} expert insights on solar design.`;
    let cleaned = this.removeEmbeddedJsonFragments(desc);
    cleaned = cleaned.replace(/\s+/g, ' ').trim();
    if (cleaned.length < 140) {
      // expand with a short marketing clause
      cleaned = (cleaned + ' ' + `${companyName} offers design and permit-ready plan sets for projects of all sizes.`).trim();
    }
    if (cleaned.length > 160) cleaned = cleaned.substring(0, 157).trim() + '...';
    return cleaned;
  }

  /**
   * Ensure at least one H2 contains the exact keyword; if none, inject a specific H2 after the intro.
   */
  ensureH2WithKeyword(blocks, keyword) {
    const kwEsc = keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const exactRegex = new RegExp(`\\b${kwEsc}\\b`, 'i');
    const h2With = blocks.find(b => b.type === 'h2' && exactRegex.test(b.content || ''));
    if (h2With) return;
    // find intro index
    const introIndex = blocks.findIndex(b => b.id && b.id.startsWith('intro-'));
  const headingText = `${keyword}: Key Considerations`;
  const inject = { id: `h2-auto-${Date.now()}`, type: 'h2', content: headingText, contentHtml: `<h2>${headingText}</h2>`, wordCount: this.countWordsFromHtml(headingText), seoNotes: 'Auto-inserted H2 with keyword' };
    if (introIndex >= 0) {
      blocks.splice(introIndex + 1, 0, inject);
    } else {
      blocks.unshift(inject);
    }
  }

  /**
   * Ensure at least one image alt contains the keyword; add a tiny invisible img with alt text if none found.
   */
  ensureImageAltWithKeyword(blocks, keyword) {
    const altRegex = new RegExp(`alt=\"[^\"]*${keyword}[^\"]*\"`, 'i');
    const found = blocks.some(b => typeof b.content === 'string' && altRegex.test(b.content));
    if (found) return;
    // Insert an invisible 1x1 PNG data URI with alt text near the end of the last paragraph
    const lastParaIdx = (() => { for (let i = blocks.length - 1; i >= 0; i--) if (blocks[i].type === 'paragraph') return i; return -1; })();
    const imgHtml = `<img src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///ywAAAAAAQABAAACAUwAOw==" alt="${keyword} image" style="width:1px;height:1px;border:0;display:inline-block;" />`;
    if (lastParaIdx >= 0) {
      blocks[lastParaIdx].content += ' ' + imgHtml;
      blocks[lastParaIdx].wordCount = this.countWordsFromHtml(blocks[lastParaIdx].content);
    } else {
      blocks.push({ id: `img-auto-${Date.now()}`, type: 'paragraph', content: imgHtml, wordCount: 0, seoNotes: 'Auto image alt for SEO' });
    }
  }

  /**
   * Ensure a minimum number of internal links exist across content blocks; inject inbound links into paragraphs if needed.
   */
  ensureInternalLinksCount(blocks, realLinks, minCount = 2, keyword = '') {
    const allHtml = blocks.map(b => b.content || '').join(' ');
    const currentInternal = (allHtml.match(new RegExp(`<a[^>]*href="https?:\/\/(?:www\.)?${this.escapeHostFromLinks((realLinks.inboundLinks||[]).map(l=>l.url).join('|'))}`, 'gi')) || []).length;
    // Simplify: count internal links by presence of any inbound urls
    const current = (allHtml.match(/<a [^>]*href="https?:\/\//gi) || []).length;
    if (current >= minCount) return;

    const needed = Math.max(0, minCount - current);
    const inbound = (realLinks.inboundLinks || []).filter(l => l.url && l.text).slice(0, needed);
    // Inject inbound links at ends of last paragraphs
    let injected = 0;
    for (let i = blocks.length -1; i >=0 && injected < inbound.length; i--) {
      const b = blocks[i];
      if (b.type !== 'paragraph' || !b.content) continue;
      const link = inbound[injected];
  const s4 = this.sanitizeLink(link);
  if (s4 && s4.url) b.content += ` <a href="${encodeURI(s4.url)}" target="_blank" rel="noopener noreferrer">${this.escapeHtml(this.createShortLinkText(s4.text, keyword))}</a>`;
      b.wordCount = this.countWordsFromHtml(b.content);
      injected++;
    }
  }

  escapeHostFromLinks(pattern) {
    return pattern.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&');
  }

  /**
   * Ensure total word count meets the minimum by expanding last paragraph or the conclusion using the LLM.
   */
  async ensureMinimumWordCount(blocks, minWords, keyword, companyContext = {}) {
    const total = blocks.reduce((s,b)=> s + (b.wordCount || 0), 0);
    if (total >= minWords) return;
    const deficit = minWords - total;
    // Find a paragraph to expand: prefer conclusion, then last paragraph
    let targetIdx = blocks.findIndex(b => b.id && b.id.startsWith('conclusion-'));
    if (targetIdx === -1) targetIdx = (() => { for (let i = blocks.length -1; i>=0; i--) if (blocks[i].type === 'paragraph') return i; return -1; })();
    if (targetIdx === -1) return;

    const target = blocks[targetIdx];
    const expandPrompt = `Expand the following paragraph by approximately ${deficit} words, preserving technical accuracy and sentence integrity. Include the exact phrase "${keyword}" at least once. Return only HTML content.` + `\n\nCONTENT:\n${target.content}`;
    try {
      const vertex = require('./geminiService');
      const res = await vertex.generateContent(expandPrompt, companyContext);
      const cleaned = this.removeEmbeddedJsonFragments(res.content || res || '');
      target.content = (target.content + '\n\n' + cleaned).trim();
      target.wordCount = this.countWordsFromHtml(target.content);
    } catch (e) {
      // fallback: duplicate a short sentence to approximate length
      const filler = `<p>${keyword} insights continue. ${'Further detail. '.repeat(Math.min(50, Math.ceil(deficit/3)))}</p>`;
      target.content += '\n' + filler;
      target.wordCount = this.countWordsFromHtml(target.content);
    }
  }
}

module.exports = new SEOOptimizationService(); 