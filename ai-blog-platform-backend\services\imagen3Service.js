// services/imagen3Service.js
const GoogleGenAIService = require('./googleGenAIService');
const path = require('path');
require('dotenv').config();

class Imagen3Service {
  constructor() {
    // Initialize Google Gen AI service for Imagen 3
    this.genAIService = new GoogleGenAIService();

    // Backward compatibility properties
    this.project = this.genAIService.project;
    this.location = this.genAIService.location;
    this.model = this.genAIService.imageModel;
    this.vertexAI = this.genAIService.client;
    this.imageModel = this.genAIService.client;

    if (this.genAIService.isAvailable()) {
      console.log(`✅ Imagen 3 Service initialized with Google Gen AI SDK!`);
      console.log(`   Project: ${this.project}`);
      console.log(`   Location: ${this.location}`);
      console.log(`   Model: ${this.model}`);
    } else {
      console.warn('⚠️ Google Gen AI service not available. Imagen 3 service disabled.');
    }
  }

  /**
   * Generate high-quality images using Imagen 3
   * @param {string} prompt - Image generation prompt
   * @param {Object} options - Generation options
   * @returns {Promise<Object>} Generated image result
   */
  async generateImage(prompt, options = {}) {
    if (!this.genAIService.isAvailable()) {
      throw new Error('Google Gen AI service not available');
    }

    const {
      numberOfImages = 1,
      aspectRatio = '1:1',
      safetyFilterLevel = 'block_some',
      personGeneration = 'dont_allow',
      outputMimeType = 'image/jpeg'
    } = options;

    try {
      console.log(`🎨 Generating image with Google Gen AI Imagen 3: "${prompt}"`);

      // Enhanced prompt for solar industry with professional quality
      const enhancedPrompt = `${prompt}, professional solar industry photography, high quality, modern technology, clean energy, commercial grade, photorealistic, detailed, sharp focus, professional lighting, 4K resolution`;

      // Use Google Gen AI service for image generation
      const result = await this.genAIService.generateImages(enhancedPrompt, {
        numberOfImages: numberOfImages,
        aspectRatio: aspectRatio,
        safetyFilterLevel: safetyFilterLevel,
        includeRaiReason: true,
        outputMimeType: outputMimeType
      });

      if (!result.success || !result.images || result.images.length === 0) {
        throw new Error('No images generated by Google Gen AI');
      }

      const generatedImage = result.images[0];

      console.log(`✅ Google Gen AI Imagen 3 generated image successfully`);
      console.log(`   Generated ${result.images.length} image(s)`);

      // Extract image data
      const imageData = generatedImage.image?.imageBytes || generatedImage.imageBytes;

      return {
        success: true,
        imageData: imageData,
        mimeType: outputMimeType,
        generatedImage: generatedImage,
        prompt: enhancedPrompt,
        model: this.model,
        source: 'google-gen-ai-imagen-3'
      };

    } catch (error) {
      console.error('❌ Imagen 3 generation failed:', error.message);
      
      // Log specific error types
      if (error.code === 'PERMISSION_DENIED') {
        console.error('🔑 Authentication issue - check service account permissions for Imagen 3');
      } else if (error.code === 'QUOTA_EXCEEDED') {
        console.error('📊 Quota exceeded - check Imagen 3 usage limits');
      } else if (error.code === 'INVALID_ARGUMENT') {
        console.error('📝 Invalid request - check Imagen 3 parameters');
      }

      throw error;
    }
  }

  /**
   * Generate dynamic image prompts optimized for Imagen 3
   * @param {string} keyword - Focus keyword
   * @param {string} imageType - Type of image (feature, content, etc.)
   * @param {string} companyName - Company name for branding
   * @returns {string} Optimized prompt
   */
  generateOptimizedPrompt(keyword, imageType = 'feature', companyName = 'WattMonk') {
    const basePrompts = {
      feature: [
        `Professional ${keyword} installation with modern solar technology`,
        `High-tech ${keyword} system with advanced solar equipment`,
        `Commercial ${keyword} setup with professional grade components`,
        `Industrial ${keyword} facility with cutting-edge solar technology`
      ],
      content: [
        `Technical diagram showing ${keyword} components and connections`,
        `Step-by-step ${keyword} installation process with professional workers`,
        `Detailed view of ${keyword} equipment with technical specifications`,
        `Professional team working on ${keyword} project with safety equipment`
      ],
      technical: [
        `Close-up view of ${keyword} technical components and wiring`,
        `Engineering blueprint style ${keyword} system diagram`,
        `Professional ${keyword} monitoring and control systems`,
        `Technical analysis of ${keyword} performance metrics and data`
      ]
    };

    const prompts = basePrompts[imageType] || basePrompts.feature;
    const selectedPrompt = prompts[Math.floor(Math.random() * prompts.length)];

    // Add professional quality modifiers
    const qualityModifiers = [
      'professional photography',
      'high resolution',
      'commercial quality',
      'industry standard',
      'photorealistic',
      'detailed composition',
      'professional lighting',
      'sharp focus',
      'modern aesthetic'
    ];

    const modifier = qualityModifiers[Math.floor(Math.random() * qualityModifiers.length)];

    return `${selectedPrompt}, ${modifier}, solar industry, clean energy technology, ${companyName} branding style`;
  }

  /**
   * Check if Imagen 3 service is available
   * @returns {boolean} Service availability
   */
  isAvailable() {
    // Temporarily disable Imagen 3 due to SDK compatibility issues
    return false;
    // return this.vertexAI !== null;
  }

  /**
   * Get service status and configuration
   * @returns {Object} Service status
   */
  getStatus() {
    return {
      available: this.isAvailable(),
      project: this.project,
      location: this.location,
      model: this.model,
      service: 'Imagen 3'
    };
  }
}

// Export as singleton
module.exports = new Imagen3Service();
