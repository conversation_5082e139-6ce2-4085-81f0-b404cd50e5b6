# MongoDB Configuration
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=password123
MONGO_DB_NAME=blog_platform

# Backend Environment Variables
NODE_ENV=production
PORT=5002

# Frontend Environment Variables
NEXT_PUBLIC_API_URL=http://localhost:5002/api

# API Keys (Add your actual keys here)
GEMINI_API_KEY=your_gemini_api_key_here
PERPLEXITY_API_KEY=your_perplexity_api_key_here
SERP_API_KEY=your_serp_api_key_here
SERPER_API_KEY=your_serper_api_key_here

# Google Cloud Configuration
GOOGLE_APPLICATION_CREDENTIALS=./service_account_key.json
GOOGLE_PROJECT_ID=your_google_project_id

# AWS Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=ap-south-1
AWS_S3_BUCKET=your_s3_bucket_name

# WordPress Configuration
WORDPRESS_API_URL=your_wordpress_site_url
WORDPRESS_USERNAME=your_wordpress_username
WORDPRESS_PASSWORD=your_wordpress_password

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
