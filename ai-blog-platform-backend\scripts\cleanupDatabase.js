// scripts/cleanupDatabase.js
const mongoose = require('mongoose');
const dotenv = require('dotenv');
const TrendData = require('../models/TrendData');
const ContentBlock = require('../models/ContentBlock');
const Draft = require('../models/Draft');
const BlogData = require('../models/BlogData');
const Keyword = require('../models/Keyword');

dotenv.config();

async function cleanupDatabase() {
  try {
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');

    console.log('🧹 Starting comprehensive database cleanup...');

    // 1. Remove orphaned drafts (drafts without valid blog references)
    console.log('\n📋 Cleaning up orphaned drafts...');
    const allBlogs = await BlogData.find({}, '_id');
    const validBlogIds = allBlogs.map(blog => blog._id.toString());

    const orphanedDrafts = await Draft.find({
      $or: [
        { blogId: { $exists: false } },
        { blogId: null },
        { blogId: { $nin: validBlogIds } }
      ]
    });

    console.log(`Found ${orphanedDrafts.length} orphaned drafts`);

    for (const draft of orphanedDrafts) {
      // Delete associated content blocks
      if (draft.contentBlocks && draft.contentBlocks.length > 0) {
        await ContentBlock.deleteMany({ _id: { $in: draft.contentBlocks } });
        console.log(`  🗑️ Deleted ${draft.contentBlocks.length} content blocks for draft ${draft._id}`);
      }

      // Delete the draft
      await Draft.findByIdAndDelete(draft._id);
      console.log(`  🗑️ Deleted orphaned draft: ${draft._id}`);
    }

    // 2. Remove orphaned content blocks (blocks without valid blog references)
    console.log('\n📄 Cleaning up orphaned content blocks...');
    const deletedBlocks = await ContentBlock.deleteMany({
      blogId: { $nin: validBlogIds }
    });
    console.log(`🧹 Deleted ${deletedBlocks.deletedCount} orphaned content blocks`);

    // 3. Remove old trend data (older than 30 days)
    console.log('\n📈 Cleaning up old trend data...');
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const deletedTrends = await TrendData.deleteMany({
      createdAt: { $lt: thirtyDaysAgo }
    });
    console.log(`🧹 Deleted ${deletedTrends.deletedCount} old trend records`);

    // 4. Clean up keyword usage statistics (CRITICAL FIX)
    console.log('\n🔑 Recalculating keyword usage statistics...');
    const keywords = await Keyword.find({});

    for (const keyword of keywords) {
      // Only count keywords from ACTIVE blogs and drafts
      const activeBlogCount = await BlogData.countDocuments({
        focusKeyword: { $regex: keyword.keyword, $options: 'i' },
        status: { $in: ['pending', 'in-progress', 'completed'] } // Exclude published
      });

      const activeDraftCount = await Draft.countDocuments({
        selectedKeyword: { $regex: keyword.keyword, $options: 'i' },
        status: { $ne: 'ready_to_publish' } // Only count drafts in progress
      });

      const totalUsage = activeBlogCount + activeDraftCount;

      await Keyword.findByIdAndUpdate(keyword._id, {
        usageCount: totalUsage,
        lastUsed: totalUsage > 0 ? new Date() : keyword.lastUsed
      });

      if (totalUsage !== keyword.usageCount) {
        console.log(`  📊 Updated "${keyword.keyword}": ${keyword.usageCount} → ${totalUsage}`);
      }
    }
    console.log(`📊 Updated usage statistics for ${keywords.length} keywords`);

    // 5. Remove duplicate keywords
    console.log('\n🔄 Removing duplicate keywords...');
    const duplicateKeywords = await Keyword.aggregate([
      {
        $group: {
          _id: { keyword: { $toLower: "$keyword" } },
          ids: { $push: "$_id" },
          count: { $sum: 1 }
        }
      },
      {
        $match: { count: { $gt: 1 } }
      }
    ]);

    let duplicatesRemoved = 0;
    for (const duplicate of duplicateKeywords) {
      // Keep the first one, delete the rest
      const idsToDelete = duplicate.ids.slice(1);
      await Keyword.deleteMany({ _id: { $in: idsToDelete } });
      duplicatesRemoved += idsToDelete.length;
      console.log(`  🗑️ Removed ${idsToDelete.length} duplicates of keyword`);
    }
    console.log(`🧹 Removed ${duplicatesRemoved} duplicate keywords`);

    console.log('\n🎉 Database cleanup completed successfully!');
    console.log('✅ All orphaned data removed');
    console.log('✅ Keyword usage statistics corrected');
    console.log('✅ Database optimized for Docker deployment');

    process.exit(0);
  } catch (error) {
    console.error('❌ Database cleanup failed:', error);
    process.exit(1);
  }
}
cleanupDatabase();