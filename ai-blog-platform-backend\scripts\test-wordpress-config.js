// Test script to verify WordPress configuration for Wattmonk
const mongoose = require('mongoose');
require('dotenv').config();

// Import models and services
const Company = require('../models/Company');
const WordPressService = require('../services/wordpressService');

async function testWordPressConfig() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Find the Wattmonk company
    const companyId = '689c61e3b0d43ab00511dd0e';
    const company = await Company.findById(companyId);
    
    if (!company) {
      console.error(`❌ Company not found with ID: ${companyId}`);
      process.exit(1);
    }

    console.log(`✅ Found company: ${company.name}`);
    console.log(`📋 Current WordPress config:`, company.wordpressConfig);

    // Test the WordPress service
    const wordpressService = new WordPressService();
    
    console.log('\n🔧 Testing WordPress configuration...');
    
    try {
      const config = await wordpressService.getCompanyWordPressConfig(companyId);
      console.log('✅ WordPress configuration retrieved successfully!');
      console.log('📋 Configuration details:', {
        baseUrl: config.baseUrl,
        username: config.username,
        hasAppPassword: !!config.appPassword,
        isActive: config.isActive
      });

      // Test connection
      console.log('\n🔍 Testing WordPress connection...');
      const connectionTest = await wordpressService.testConnection(companyId);
      
      if (connectionTest.success) {
        console.log('✅ WordPress connection successful!');
        console.log('📋 Connection details:', connectionTest);
      } else {
        console.log('❌ WordPress connection failed:', connectionTest.error);
      }

    } catch (error) {
      console.error('❌ Error testing WordPress configuration:', error.message);
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('✅ Disconnected from MongoDB');
  }
}

// Run the test
testWordPressConfig();