// services/geminiService.js
const GoogleGenAIService = require('./googleGenAIService');
const path = require('path');
require('dotenv').config();
class GeminiService {
  constructor() {
    // Initialize Google Gen AI service
    this.genAIService = new GoogleGenAIService();
    
    // Backward compatibility properties
    this.project = this.genAIService.project;
    this.location = this.genAIService.location;
    this.model = this.genAIService.textModel;
    this.vertexAI = this.genAIService.client;
    this.generativeModel = this.genAIService.client;
    
    if (this.genAIService.isAvailable()) {
      console.log(`:white_check_mark: Gemini Service initialized with Google Gen AI SDK!`);
      console.log(`   Project: ${this.project}`);
      console.log(`   Location: ${this.location}`);
      console.log(`   Model: ${this.model}`);
      console.log(`   Authentication: Service Account`);
      console.log(`   SDK: @google/genai (latest)`);
      console.log(`   Features: Google Search Grounding enabled`);
    } else {
      console.warn(':warning: Google Gen AI service not available. Using fallback content generation.');
    }
  }
  /**
   * Generate content using the new Google Gen AI SDK with optional Google Search grounding
   */
  async generateContent(prompt, options = {}) {
    console.log(':white_check_mark: Company context validated for: ' + (options.companyName || 'Unknown'));
    console.log(':robot_face: Generating content with Vertex AI...');
    console.log(`:memo: Prompt length: ${prompt.length} characters`);
    
    // Check if grounding is requested
    if (options.enableGrounding) {
      console.log(':mag: Google Search grounding enabled');
    }
    
    // Try Google Gen AI first
    if (this.genAIService.isAvailable()) {
      try {
        // Use grounded generation if requested
        if (options.enableGrounding) {
          return await this.generateGroundedContent(prompt, options);
        }
        
        // Regular content generation
        const result = await this.genAIService.generateContent(prompt, {
          temperature: options.temperature || 0.7,
          maxTokens: options.maxOutputTokens || 4096,
          model: options.model || this.model
        });
        
        if (result.success && result.content) {
          const wordCount = result.content.split(' ').length;
          console.log(`:white_check_mark: Google Gen AI final content: ${wordCount} words`);
          return {
            success: true,
            content: result.content,
            wordCount: wordCount,
            model: result.model,
            usage: result.usage
          };
        }
      } catch (error) {
        console.warn(':warning: Google Gen AI failed, trying fallback:', error.message);
      }
    }
    
    // Fallback to direct Gemini API if available
    if (process.env.GEMINI_API_KEY) {
      try {
        console.log(':arrows_counterclockwise: Using Gemini API fallback...');
        return await this.generateContentWithGeminiAPI(prompt, options);
      } catch (fallbackError) {
        console.error(':x: Gemini API fallback also failed:', fallbackError.message);
      }
    }
    
    throw new Error('All content generation methods failed');
  }
  /**
   * Generate content grounded with Google Search
   */
  async generateGroundedContent(prompt, options = {}) {
    const { GoogleGenerativeAI, DynamicRetrievalConfigMode } = require('@google/generative-ai');
    
    try {
      const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY);
      const modelName = options.model || 'gemini-2.0-flash';
      
      // Determine if we should use legacy grounding for Gemini 1.5 models
      const isLegacyModel = modelName.includes('1.5');
      
      let tools;
      if (isLegacyModel && options.dynamicGrounding) {
        // Use legacy google_search_retrieval for Gemini 1.5 with dynamic mode
        tools = [{
          googleSearchRetrieval: {
            dynamicRetrievalConfig: {
              mode: DynamicRetrievalConfigMode.MODE_DYNAMIC,
              dynamicThreshold: options.dynamicThreshold || 0.7
            }
          }
        }];
        console.log(`:mag: Using legacy dynamic grounding with threshold: ${options.dynamicThreshold || 0.7}`);
      } else {
        // Use modern google_search tool for Gemini 2.0+ or forced grounding
        tools = [{
          googleSearch: {}
        }];
        console.log(':mag: Using Google Search grounding');
      }
      
      const model = genAI.getGenerativeModel({ 
        model: modelName,
        tools: tools,
        generationConfig: {
          temperature: options.temperature || 0.7,
          maxOutputTokens: options.maxOutputTokens || 4096,
          topP: options.topP || 0.95,
          topK: options.topK || 40
        }
      });
      
      console.log(`:arrows_counterclockwise: Generating grounded content with model: ${modelName}`);
      const result = await model.generateContent(prompt);
      const response = await result.response;
      
      // Extract text and grounding metadata
      const text = response.text();
      const candidate = response.candidates?.[0];
      const groundingMetadata = candidate?.groundingMetadata;
      
      if (!text) {
        throw new Error('No content generated from Gemini API');
      }
      
      // Process citations if grounding was used
      let processedContent = text;
      let citations = [];
      let searchQueries = [];
      
      if (groundingMetadata) {
        console.log(':white_check_mark: Content grounded with Google Search');
        
        // Extract search queries
        searchQueries = groundingMetadata.webSearchQueries || [];
        console.log(`:mag: Search queries used: ${searchQueries.join(', ')}`);
        
        // Process citations if requested
        if (options.includeCitations) {
          const citationResult = this.addInlineCitations(text, groundingMetadata);
          processedContent = citationResult.textWithCitations;
          citations = citationResult.citations;
          console.log(`:link: Added ${citations.length} citations to content`);
        }
        
        // Log grounding chunks for debugging
        if (groundingMetadata.groundingChunks) {
          console.log(`:bookmark: Found ${groundingMetadata.groundingChunks.length} source chunks`);
        }
      } else if (isLegacyModel && options.dynamicGrounding) {
        console.log(':information_source: Model answered from its own knowledge (confidence below threshold)');
      }
      
      const wordCount = processedContent.split(' ').length;
      console.log(`:white_check_mark: Grounded content generated: ${wordCount} words`);
      
      return {
        success: true,
        content: processedContent,
        wordCount: wordCount,
        model: modelName,
        grounded: !!groundingMetadata,
        groundingMetadata: groundingMetadata,
        citations: citations,
        searchQueries: searchQueries,
        source: 'gemini-grounded'
      };
      
    } catch (error) {
      console.error('Grounded content generation error:', error);
      throw error;
    }
  }
  /**
   * Add inline citations to grounded content
   */
  addInlineCitations(text, groundingMetadata) {
    if (!groundingMetadata?.groundingSupports || !groundingMetadata?.groundingChunks) {
      return { textWithCitations: text, citations: [] };
    }
    
    const supports = groundingMetadata.groundingSupports;
    const chunks = groundingMetadata.groundingChunks;
    const citations = [];
    
    // Sort supports by endIndex in descending order to avoid shifting issues
    const sortedSupports = [...supports].sort((a, b) => 
      (b.segment?.endIndex || 0) - (a.segment?.endIndex || 0)
    );
    
    let citedText = text;
    
    for (const support of sortedSupports) {
      const endIndex = support.segment?.endIndex;
      
      if (endIndex === undefined || !support.groundingChunkIndices?.length) {
        continue;
      }
      
      // Create citation links
      const citationLinks = [];
      for (const i of support.groundingChunkIndices) {
        if (i < chunks.length) {
          const chunk = chunks[i];
          const uri = chunk.web?.uri;
          const title = chunk.web?.title || `Source ${i + 1}`;
          
          if (uri) {
            citationLinks.push(`[${i + 1}](${uri})`);
            
            // Add to citations array if not already present
            if (!citations.find(c => c.uri === uri)) {
              citations.push({
                index: i + 1,
                uri: uri,
                title: title,
                segment: support.segment?.text
              });
            }
          }
        }
      }
      
      if (citationLinks.length > 0) {
        const citationString = citationLinks.join(', ');
        citedText = citedText.slice(0, endIndex) + citationString + citedText.slice(endIndex);
      }
    }
    
    return {
      textWithCitations: citedText,
      citations: citations
    };
  }
  /**
   * Generate grounded content with specific search parameters
   */
  async searchAndGenerate(searchQuery, generatePrompt, options = {}) {
    console.log(':mag: Performing targeted search and generation');
    console.log(`:mag_right: Search query: ${searchQuery}`);
    
    // Create a combined prompt that will trigger search
    const combinedPrompt = options.customPrompt || 
      `Search for information about: ${searchQuery}\n\nThen, ${generatePrompt}`;
    
    // Enable grounding by default for this method
    const groundingOptions = {
      ...options,
      enableGrounding: true,
      includeCitations: options.includeCitations !== false // Default to true
    };
    
    return await this.generateContent(combinedPrompt, groundingOptions);
  }
  /**
   * Check facts using Google Search grounding
   */
  async factCheck(statement, options = {}) {
    console.log(':white_check_mark: Fact-checking statement with Google Search');
    
    const prompt = `
      Fact-check the following statement using current web information:
      "${statement}"
      
      Provide:
      1. Whether the statement is accurate, partially accurate, or inaccurate
      2. Supporting evidence from web sources
      3. Any important context or nuances
    `;
    
    const groundingOptions = {
      ...options,
      enableGrounding: true,
      includeCitations: true,
      temperature: 0.3 // Lower temperature for factual accuracy
    };
    
    const result = await this.generateContent(prompt, groundingOptions);
    
    // Parse the fact-check result
    if (result.success) {
      return {
        ...result,
        factChecked: true,
        statement: statement
      };
    }
    
    return result;
  }
  /**
   * Get real-time information using Google Search grounding
   */
  async getRealTimeInfo(query, options = {}) {
    console.log(':clock1: Fetching real-time information');
    
    const prompt = `
      Provide current, up-to-date information about: ${query}
      
      Focus on the most recent developments and cite your sources.
    `;
    
    const groundingOptions = {
      ...options,
      enableGrounding: true,
      includeCitations: true,
      model: options.model || 'gemini-2.0-flash' // Use newer model for better grounding
    };
    
    return await this.generateContent(prompt, groundingOptions);
  }
  /**
   * Fallback method using direct Gemini API with grounding support
   */
  async generateContentWithGeminiAPI(prompt, options = {}) {
    const { GoogleGenerativeAI } = require('@google/generative-ai');
    
    try {
      const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
      
      // Configure model with or without grounding
      const modelConfig = {
        model: options.model || 'gemini-1.5-flash'
      };
      
      // Add grounding tools if requested
      if (options.enableGrounding) {
        modelConfig.tools = [{
          googleSearch: {}
        }];
        console.log(':mag: Grounding enabled in fallback mode');
      }
      
      const model = genAI.getGenerativeModel(modelConfig);
      
      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();
      
      if (!text) {
        throw new Error('No content generated from Gemini API');
      }
      
      // Check for grounding metadata
      const candidate = response.candidates?.[0];
      const groundingMetadata = candidate?.groundingMetadata;
      
      let processedContent = text;
      let citations = [];
      
      if (groundingMetadata && options.includeCitations) {
        const citationResult = this.addInlineCitations(text, groundingMetadata);
        processedContent = citationResult.textWithCitations;
        citations = citationResult.citations;
      }
      
      const wordCount = processedContent.split(' ').length;
      console.log(`:white_check_mark: Gemini API fallback content: ${wordCount} words`);
      
      return {
        success: true,
        content: processedContent,
        wordCount: wordCount,
        model: modelConfig.model,
        grounded: !!groundingMetadata,
        citations: citations,
        source: 'gemini-api-fallback'
      };
    } catch (error) {
      console.error('Gemini API fallback error:', error);
      throw error;
    }
  }
  /**
   * Generate content with streaming (updated with grounding support)
   */
  async generateContentStream(prompt, options = {}) {
    if (!this.genAIService.isAvailable() && !process.env.GEMINI_API_KEY) {
      throw new Error('Google Gen AI service not available');
    }
    
    try {
      if (options.enableGrounding) {
        // Streaming with grounding requires special handling
        console.log(':warning: Streaming with grounding enabled - citations will be available at the end');
        
        const { GoogleGenerativeAI } = require('@google/generative-ai');
        const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY);
        
        const tools = [{
          googleSearch: {}
        }];
        
        const model = genAI.getGenerativeModel({ 
          model: options.model || 'gemini-2.0-flash',
          tools: tools
        });
        
        const result = await model.generateContentStream(prompt);
        
        // Return an async generator that yields chunks
        return {
          stream: result.stream,
          response: result.response // This will contain grounding metadata when complete
        };
      }
      
      // Regular streaming without grounding
      return await this.genAIService.generateContentStream(prompt, options);
    } catch (error) {
      console.error('Streaming content generation error:', error);
      throw error;
    }
  }
  /**
   * Count tokens in content
   */
  async countTokens(content, model = this.model) {
    if (!this.genAIService.isAvailable()) {
      return { totalTokens: Math.ceil(content.length / 4) }; // Rough estimate
    }
    
    try {
      return await this.genAIService.countTokens(content, model);
    } catch (error) {
      console.error('Token counting error:', error);
      return { totalTokens: Math.ceil(content.length / 4) }; // Fallback estimate
    }
  }
  /**
   * Check if service is available
   */
  isAvailable() {
    return (this.genAIService && this.genAIService.isAvailable()) || !!process.env.GEMINI_API_KEY;
  }
  /**
   * Get service status
   */
  getStatus() {
    const baseStatus = this.genAIService ? this.genAIService.getStatus() : {
      available: false,
      service: 'Gemini (Google Gen AI)',
      error: 'Service not initialized'
    };
    
    // Add grounding capability status
    return {
      ...baseStatus,
      capabilities: {
        grounding: true,
        realTimeSearch: true,
        citations: true,
        factChecking: true
      }
    };
  }
  /**
   * Enhance content with company information and context
   */
  enhanceContentWithCompanyInfo(content, companyContext, keyword) {
    try {
      if (!content || !companyContext) {
        return content;
      }
      
      let enhancedContent = content;
      
      // Add company name if not present
      if (companyContext.name && !enhancedContent.toLowerCase().includes(companyContext.name.toLowerCase())) {
        // Find a good place to insert company name naturally
        const sentences = enhancedContent.split('. ');
        if (sentences.length > 1) {
          // Insert company name in the second sentence if possible
          sentences[1] = sentences[1].replace(/\b(we|our company|the company)\b/gi, companyContext.name);
          enhancedContent = sentences.join('. ');
        }
      }
      
      // Add relevant company services if appropriate
      if (companyContext.servicesOffered && keyword) {
        const services = companyContext.servicesOffered.toLowerCase();
        const keywordLower = keyword.toLowerCase();
        
        // If the keyword relates to services offered, mention it
        if (services.includes(keywordLower) || keywordLower.includes('solar')) {
          if (!enhancedContent.toLowerCase().includes('expert') && !enhancedContent.toLowerCase().includes('professional')) {
            enhancedContent = enhancedContent.replace(
              /\b(solutions|services|systems)\b/gi,
              'expert $1'
            );
          }
        }
      }
      
      return enhancedContent;
    } catch (error) {
      console.warn(':warning: Error enhancing content with company info:', error.message);
      return content; // Return original content if enhancement fails
    }
  }
  /**
   * Image generation method - redirects to image service
   */
  async generateImages(prompt, options = {}) {
    try {
      console.log(':art: Image generation requested via Gemini service, redirecting to image service...');
      
      // Import image service dynamically to avoid circular dependencies
      const imageService = require('./imageService');
      
      // Extract options with defaults
      const {
        style = 'realistic',
        imageType = 'featured',
        blogTitle = '',
        customTitle = ''
      } = options;
      
      // Call the dedicated image service
      const result = await imageService.generateImageWithAI(prompt, style, imageType, blogTitle, customTitle);
      
      return {
        success: true,
        images: [result], // Return as array to match expected format
        message: 'Image generated successfully'
      };
    } catch (error) {
      console.error('Image generation error:', error);
      
      // Return a structured error response
      return {
        success: false,
        images: [],
        error: error.message || 'Image generation failed',
        message: 'Image generation is not directly supported by Gemini. Please use the dedicated image service.'
      };
    }
  }
}
// Create the main service instance
const geminiServiceInstance = new GeminiService();
module.exports = geminiServiceInstance;