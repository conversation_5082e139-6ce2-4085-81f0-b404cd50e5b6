version: "3.8"
services:
  backend:
    build: ./ai-blog-platform-backend
    ports:
      - "5002:5002"
    environment:
      MONGO_URI: mongodb://mongo:27017/ai-blog
      PORT: 5002
    depends_on:
      - mongo
    restart: always

  frontend:
    build: ./ai-blog-platform-frontend
    ports:
      - "3001:3001"
    environment:
      NEXT_PUBLIC_API_URL: http://localhost:5002/api
    depends_on:
      - backend
    restart: always

  mongo:
    image: "mongo:latest"
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db
    restart: always

volumes:
  mongo_data:
