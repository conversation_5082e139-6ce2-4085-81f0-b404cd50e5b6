// routes/imageRoutes.js
const express = require('express');
const imageService = require('../services/imageService');
const geminiService = require('../services/geminiService');
const router = express.Router();

// GET imageModel for frontend compatibility
router.get('/model', (req, res) => {
  try {
    // Return the imageModel object that the frontend expects
    res.json({
      success: true,
      imageModel: geminiService.imageModel,
      message: 'Image model available'
    });
  } catch (error) {
    console.error('Image model access error:', error);
    res.status(500).json({
      success: false,
      message: 'Image model not available',
      error: error.message
    });
  }
});

// POST generate images via imageModel (for frontend compatibility)
router.post('/model/generate', async (req, res) => {
  try {
    const { prompt, options = {} } = req.body;

    if (!prompt) {
      return res.status(400).json({
        success: false,
        message: 'Prompt is required'
      });
    }

    console.log(`🎨 Image generation via imageModel: "${prompt}"`);

    // QUICK FIX: Use direct image service instead of imageModel
    try {
      const imageService = require('../services/imageService');
      const result = await imageService.generateImageWithAI(
        prompt,
        options.style || 'realistic',
        options.imageType || 'featured',
        options.blogTitle || '',
        options.customTitle || ''
      );

      return res.json({
        success: true,
        images: [result],
        data: result,
        message: 'Image generated successfully'
      });
    } catch (imageError) {
      console.error('Direct image service error:', imageError);

      // Fallback response
      return res.json({
        success: false,
        images: [],
        error: imageError.message,
        message: 'Image generation failed - using fallback'
      });
    }
  } catch (error) {
    console.error('Image model generation error:', error);
    res.status(500).json({
      success: false,
      message: error.message,
      error: 'Image generation failed'
    });
  }
});

// POST generate AI image
router.post('/generate', async (req, res) => {
  try {
    const { prompt, style = 'realistic', imageType = 'featured', draftId, blockId, blogTitle, customTitle } = req.body;

    if (!prompt) {
      return res.status(400).json({ message: 'Prompt is required' });
    }

    console.log(`🎨 Image generation request: "${prompt}" (style: ${style}, type: ${imageType})`);
    if (customTitle) {
      console.log(`🏷️ Using custom title: "${customTitle}"`);
    }

    // Use provided blog title or empty string
    const titleForImage = blogTitle || '';
    const result = await imageService.generateImageWithAI(prompt, style, imageType, titleForImage, customTitle);

    // If this is for a specific content block, we could save the association
    if (draftId && blockId) {
      console.log(`📎 Generated image for draft ${draftId}, block ${blockId}`);
      result.draftId = draftId;
      result.blockId = blockId;
    }

    res.json(result);
  } catch (error) {
    console.error('Image generation error:', error);
    res.status(500).json({ message: error.message });
  }
});

// POST upload image
router.post('/upload', imageService.getUploadMiddleware(), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }
    
    const result = await imageService.uploadImage(req.file);
    res.json(result);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

module.exports = router;
