#!/bin/bash

# AI Blog Platform - Production Deployment Script
# Usage: ./deploy.sh [command]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker Desktop and try again."
        exit 1
    fi
    print_success "Docker is running"
}

# Check if ports are available
check_ports() {
    local ports=(3001 5002 27017)
    for port in "${ports[@]}"; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            print_warning "Port $port is already in use"
            print_status "You may need to stop the service using this port"
        else
            print_success "Port $port is available"
        fi
    done
}

# Deploy the application
deploy() {
    print_status "Starting AI Blog Platform deployment..."
    
    check_docker
    check_ports
    
    print_status "Building and starting services..."
    docker-compose -f docker-compose.prod.yml up -d --build
    
    print_status "Waiting for services to start..."
    sleep 10
    
    print_status "Checking service health..."
    docker-compose -f docker-compose.prod.yml ps
    
    print_success "Deployment complete!"
    echo ""
    echo "🎉 AI Blog Platform is now running:"
    echo "   Frontend: http://localhost:3001"
    echo "   Backend:  http://localhost:5002/api"
    echo "   Health:   http://localhost:5002/health"
    echo ""
    echo "📊 To check status: ./deploy.sh status"
    echo "📋 To view logs:   ./deploy.sh logs"
    echo "🛑 To stop:        ./deploy.sh stop"
}

# Start services
start() {
    print_status "Starting AI Blog Platform..."
    docker-compose -f docker-compose.prod.yml up -d
    print_success "Services started"
}

# Stop services
stop() {
    print_status "Stopping AI Blog Platform..."
    docker-compose -f docker-compose.prod.yml down
    print_success "Services stopped"
}

# Show status
status() {
    print_status "AI Blog Platform Status:"
    docker-compose -f docker-compose.prod.yml ps
}

# Show logs
logs() {
    print_status "AI Blog Platform Logs:"
    docker-compose -f docker-compose.prod.yml logs -f --tail=50
}

# Clean everything
clean() {
    print_warning "This will remove all containers, images, and volumes!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Cleaning up..."
        docker-compose -f docker-compose.prod.yml down -v --rmi all
        print_success "Cleanup complete"
    else
        print_status "Cleanup cancelled"
    fi
}

# Rebuild images
build() {
    print_status "Rebuilding images..."
    docker-compose -f docker-compose.prod.yml build --no-cache
    print_success "Images rebuilt"
}

# Show help
help() {
    echo "AI Blog Platform - Deployment Script"
    echo ""
    echo "Usage: ./deploy.sh [command]"
    echo ""
    echo "Commands:"
    echo "  deploy    - Deploy the complete application (recommended for first time)"
    echo "  start     - Start existing services"
    echo "  stop      - Stop all services"
    echo "  status    - Show service status"
    echo "  logs      - Show service logs"
    echo "  build     - Rebuild Docker images"
    echo "  clean     - Remove all containers and images"
    echo "  help      - Show this help message"
    echo ""
    echo "Examples:"
    echo "  ./deploy.sh deploy    # First time deployment"
    echo "  ./deploy.sh status    # Check if services are running"
    echo "  ./deploy.sh logs      # View application logs"
}

# Main script logic
case "${1:-help}" in
    deploy)
        deploy
        ;;
    start)
        start
        ;;
    stop)
        stop
        ;;
    status)
        status
        ;;
    logs)
        logs
        ;;
    build)
        build
        ;;
    clean)
        clean
        ;;
    help|*)
        help
        ;;
esac