version: "3.8"

services:
  backend:
    build: 
      context: ./ai-blog-platform-backend
      dockerfile: Dockerfile
    ports:
      - "5002:5002"
    environment:
      NODE_ENV: production
      PORT: 5002
      MONGODB_URI: mongodb://mongo:27017/ai-blog-platform
      FRONTEND_URL: http://localhost:3001
    depends_on:
      - mongo
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:5002/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  frontend:
    build: 
      context: ./ai-blog-platform-frontend
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      NODE_ENV: production
      NEXT_PUBLIC_API_URL: http://localhost:5002/api
    depends_on:
      - backend
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3001"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  mongo:
    image: "mongo:7-jammy"
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db
      - ./mongo-init:/docker-entrypoint-initdb.d:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

volumes:
  mongo_data:
    driver: local

networks:
  default:
    name: ai-blog-platform