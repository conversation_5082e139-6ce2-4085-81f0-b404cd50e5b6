// Script to fix WordPress configuration for Wattmonk company
const mongoose = require('mongoose');
require('dotenv').config();

// Import the Company model
const Company = require('../models/Company');

async function fixWordPressConfig() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/ai-blog-platform');
    console.log('✅ Connected to MongoDB');

    // Find the Wattmonk company
    const companyId = '689c61e3b0d43ab00511dd0e';
    const company = await Company.findById(companyId);
    
    if (!company) {
      console.error(`❌ Company not found with ID: ${companyId}`);
      process.exit(1);
    }

    console.log(`✅ Found company: ${company.name}`);
    console.log(`📋 Current WordPress config:`, company.wordpressConfig);

    // Update with WordPress configuration
    // REPLACE THESE VALUES WITH YOUR ACTUAL WORDPRESS DETAILS
    const wordpressConfig = {
      baseUrl: process.env.WORDPRESS_BASE_URL || 'https://demo.wattmonk.com', // Replace with actual WordPress URL
      username: process.env.WORDPRESS_USERNAME || 'demo-user',                // Replace with actual WordPress username
      appPassword: process.env.WORDPRESS_APP_PASSWORD || 'demo-pass-1234',    // Replace with actual WordPress app password
      isActive: true,
      connectionStatus: 'not-tested'
    };

    // Update the company
    const updatedCompany = await Company.findByIdAndUpdate(
      companyId,
      { wordpressConfig },
      { new: true }
    );

    console.log('✅ WordPress configuration updated successfully!');
    console.log('📋 New configuration:', {
      baseUrl: updatedCompany.wordpressConfig.baseUrl,
      username: updatedCompany.wordpressConfig.username,
      hasAppPassword: !!updatedCompany.wordpressConfig.appPassword,
      isActive: updatedCompany.wordpressConfig.isActive
    });

    console.log('\n🔧 Next steps:');
    console.log('1. Make sure your WordPress site has Application Passwords enabled');
    console.log('2. Generate an Application Password in WordPress Admin → Users → Your Profile');
    console.log('3. Test the connection using the WordPress setup page in the frontend');

  } catch (error) {
    console.error('❌ Error fixing WordPress configuration:', error);
  } finally {
    await mongoose.disconnect();
    console.log('✅ Disconnected from MongoDB');
  }
}

// Run the script
fixWordPressConfig();