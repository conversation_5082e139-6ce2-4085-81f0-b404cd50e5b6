// services/performanceOptimizationService.js
const NodeCache = require('node-cache');

class PerformanceOptimizationService {
  constructor() {
    // Cache with 30 minute TTL for content, 1 hour for meta data
    this.contentCache = new NodeCache({ stdTTL: 1800 }); // 30 minutes
    this.metaCache = new NodeCache({ stdTTL: 3600 }); // 1 hour
    this.imageCache = new NodeCache({ stdTTL: 7200 }); // 2 hours
    
    console.log('🚀 Performance Optimization Service initialized with caching');
  }

  /**
   * CRITICAL: Parallel content generation to fix slow performance
   * @param {Object} contentData - Content generation parameters
   * @param {Object} options - Generation options
   * @param {boolean} options.skipImages - Skip image generation for faster loading
   */
  async generateContentInParallel(contentData, options = {}) {
    const {
      selectedKeyword,
      selectedH1,
      selectedMetaTitle,
      selectedMetaDescription,
      companyName,
      companyContext,
      targetWordCount = 2500
    } = contentData;

    console.log('🚀 PARALLEL CONTENT GENERATION STARTED');
    console.log(`   Keyword: "${selectedKeyword}"`);
    console.log(`   Target Words: ${targetWordCount}`);
    console.log(`   Company: ${companyName}`);

    const startTime = Date.now();

    try {
      // Import services
      const seoOptimizationService = require('./seoOptimizationService');
      const imageService = require('./imageService');
      const geminiService = require('./geminiService');

      // Create cache key
      const cacheKey = this.generateCacheKey(selectedKeyword, companyName, targetWordCount);
      
      // Check cache first
      const cachedContent = this.contentCache.get(cacheKey);
      if (cachedContent) {
        console.log(`⚡ Cache hit! Returning cached content in ${Date.now() - startTime}ms`);
        return cachedContent;
      }

      // PARALLEL EXECUTION - Run tasks simultaneously (conditionally skip images)
      const tasks = [
        // Task 1: Generate SEO-optimized content blocks (always required)
        seoOptimizationService.generateSEOOptimizedContent({
          selectedKeyword,
          selectedH1,
          selectedMetaTitle,
          selectedMetaDescription,
          companyName,
          companyContext,
          targetWordCount
        }),

        // Task 2: Generate conclusion and references (always required)
        this.generateConclusionAndReferencesAsync(selectedKeyword, companyName, companyContext)
      ];

      // Add image generation tasks only if not skipped
      if (!options.skipImages) {
        console.log('🖼️ Including image generation in parallel tasks');
        tasks.push(
          // Task 3: Generate feature image in parallel
          this.generateFeatureImageAsync(selectedKeyword, selectedH1, companyName),
          // Task 4: Generate content images in parallel
          this.generateContentImagesAsync(selectedKeyword, companyName)
        );
      } else {
        console.log('⚡ Skipping image generation for faster content loading');
      }

      const parallelTasks = await Promise.allSettled(tasks);

      // Process results based on what was generated
      let seoContentResult, conclusionResult, featureImageResult, contentImagesResult;

      if (options.skipImages) {
        // Text-only mode: [seoContent, conclusion]
        [seoContentResult, conclusionResult] = parallelTasks;
      } else {
        // Full mode: [seoContent, conclusion, featureImage, contentImages]
        [seoContentResult, conclusionResult, featureImageResult, contentImagesResult] = parallelTasks;
      }

      // Handle SEO content (critical)
      if (seoContentResult.status === 'rejected') {
        throw new Error(`SEO content generation failed: ${seoContentResult.reason}`);
      }
      const seoContent = seoContentResult.value;

      // Handle conclusion (use fallback if needed)
      const conclusion = conclusionResult.status === 'fulfilled'
        ? conclusionResult.value
        : this.getFallbackConclusion(selectedKeyword, companyName);

      // Handle images (generate placeholders if skipped)
      let featureImage, contentImages;

      if (options.skipImages) {
        console.log('📝 Creating image placeholders for on-demand generation');
        featureImage = this.createImagePlaceholder('feature', selectedKeyword, selectedH1);
        contentImages = this.createImagePlaceholders('content', selectedKeyword, 2);
      } else {
        // Handle images (non-critical, use fallbacks)
        featureImage = featureImageResult.status === 'fulfilled'
          ? featureImageResult.value
          : this.getFallbackFeatureImage(selectedKeyword);

        contentImages = contentImagesResult.status === 'fulfilled'
          ? contentImagesResult.value
          : this.getFallbackContentImages(selectedKeyword);
      }

      // Combine all content
      const finalContent = {
        ...seoContent,
        featureImage,
        contentImages,
        conclusion,
        generationTime: Date.now() - startTime,
        cacheKey,
        generatedAt: new Date().toISOString()
      };

      // Cache the result
      this.contentCache.set(cacheKey, finalContent);

      console.log(`🎉 PARALLEL GENERATION COMPLETE in ${Date.now() - startTime}ms`);
      console.log(`   SEO Score: ${finalContent.estimatedRankMathScore}/100`);
      console.log(`   Word Count: ${finalContent.contentBlocks?.reduce((sum, block) => sum + (block.wordCount || 0), 0) || 0}`);
      console.log(`   Images: Feature + ${contentImages.length} content images`);

      return finalContent;

    } catch (error) {
      console.error('❌ Parallel content generation failed:', error);
      
      // Return fallback content to prevent complete failure
      return this.generateFallbackContent(contentData, Date.now() - startTime);
    }
  }

  /**
   * Generate feature image asynchronously
   */
  async generateFeatureImageAsync(keyword, title, companyName) {
    const imageService = require('./imageService');
    
    const prompt = `Professional ${keyword} for ${companyName}, solar industry, modern technology, high quality, commercial grade`;
    
    return await imageService.generateImageWithAI(
      prompt,
      'realistic',
      'feature',
      title,
      `${keyword} - ${companyName}`
    );
  }

  /**
   * Generate content images asynchronously
   */
  async generateContentImagesAsync(keyword, companyName) {
    const imageService = require('./imageService');
    
    const imagePrompts = [
      `${keyword} installation process, solar panels, professional work`,
      `${keyword} benefits diagram, solar energy efficiency, modern graphics`
    ];

    const imagePromises = imagePrompts.map(async (prompt, index) => {
      try {
        return await imageService.generateImageWithAI(
          prompt,
          'realistic',
          'content',
          `${keyword} Content Image ${index + 1}`,
          `${keyword} - ${companyName}`
        );
      } catch (error) {
        console.warn(`⚠️ Content image ${index + 1} generation failed:`, error.message);
        return this.getFallbackContentImage(keyword, index + 1);
      }
    });

    const results = await Promise.allSettled(imagePromises);
    return results
      .filter(result => result.status === 'fulfilled')
      .map(result => result.value);
  }

  /**
   * Generate conclusion and references asynchronously
   */
  async generateConclusionAndReferencesAsync(keyword, companyName, companyContext) {
    const geminiService = require('./geminiService');
    
    const prompt = `Generate a professional conclusion for a blog about "${keyword}" for ${companyName}.

REQUIREMENTS:
- 150-200 words
- Include call-to-action for ${companyName}
- Summarize key benefits
- Professional tone
- NO years (2024, 2025)

Also generate 5 simple reference links in this format:
[1] Link title - https://example.com
[2] Link title - https://example.com

Return in JSON format:
{
  "conclusion": "conclusion content with HTML formatting",
  "references": ["[1] Link title - https://example.com", "[2] Link title - https://example.com"]
}`;

    const result = await geminiService.generateContent(prompt, companyContext);
    
    try {
      const parsed = JSON.parse(result.content);
      return {
        conclusion: parsed.conclusion,
        references: parsed.references || []
      };
    } catch (error) {
      return this.getFallbackConclusion(keyword, companyName);
    }
  }

  /**
   * Generate cache key for content
   */
  generateCacheKey(keyword, companyName, wordCount) {
    const keyString = `${keyword}-${companyName}-${wordCount}`;
    return keyString.toLowerCase().replace(/[^a-z0-9-]/g, '-');
  }

  /**
   * Fallback methods for failed generations
   */
  getFallbackFeatureImage(keyword) {
    return {
      success: true,
      imageUrl: `https://picsum.photos/1200/600?random=${Date.now()}`,
      prompt: `${keyword} feature image`,
      source: 'fallback',
      alt: `${keyword} - Professional Solar Solutions`
    };
  }

  getFallbackContentImage(keyword, index) {
    return {
      success: true,
      imageUrl: `https://picsum.photos/800/400?random=${Date.now() + index}`,
      prompt: `${keyword} content image ${index}`,
      source: 'fallback',
      alt: `${keyword} Content Image ${index}`
    };
  }

  getFallbackContentImages(keyword) {
    return [
      this.getFallbackContentImage(keyword, 1),
      this.getFallbackContentImage(keyword, 2)
    ];
  }

  getFallbackConclusion(keyword, companyName) {
    return {
      conclusion: `<h2 style='color: #FBD46F; font-family: Roboto; font-weight: 600;'>Conclusion</h2>
<p>Understanding <b>${keyword}</b> is essential for maximizing solar energy efficiency and return on investment. At <b>${companyName}</b>, our comprehensive approach ensures optimal results for every project.</p>
<p>Ready to implement <b>${keyword}</b> solutions? Contact <b>${companyName}</b> today for expert consultation and professional implementation support.</p>`,
      references: this.generateSimpleReferences(keyword, companyName)
    };
  }

  /**
   * Generate simple, clean reference links in [1], [2] format
   */
  generateSimpleReferences(keyword, companyName) {
    const baseReferences = [
      {
        title: 'Solar Industry Association - Industry Standards',
        url: 'https://www.seia.org/research-resources'
      },
      {
        title: 'National Renewable Energy Laboratory - Technical Resources',
        url: 'https://www.nrel.gov/solar/'
      },
      {
        title: 'U.S. Department of Energy - Solar Energy Technologies',
        url: 'https://www.energy.gov/eere/solar/solar-energy-technologies-office'
      },
      {
        title: 'International Energy Agency - Solar Power Report',
        url: 'https://www.iea.org/reports/solar-pv'
      },
      {
        title: 'Solar Power World - Industry News and Insights',
        url: 'https://www.solarpowerworldonline.com/'
      }
    ];

    // Add company-specific reference if it's WattMonk
    if (companyName.toLowerCase().includes('wattmonk')) {
      baseReferences.unshift({
        title: `${companyName} - Professional Solar Solutions`,
        url: 'https://wattmonk.com'
      });
    }

    // Format as simple [1], [2] style references
    return baseReferences.slice(0, 5).map((ref, index) => {
      return `[${index + 1}] <a href="${ref.url}" target="_blank" rel="noopener noreferrer" class="font-medium text-sm text-blue-600 hover:text-blue-800 underline">${ref.title}</a>`;
    });
  }

  /**
   * Generate complete fallback content
   */
  generateFallbackContent(contentData, generationTime) {
    const { selectedKeyword, companyName, targetWordCount } = contentData;
    
    return {
      contentBlocks: [
        {
          id: 'intro',
          type: 'introduction',
          content: `<p>Understanding <b>${selectedKeyword}</b> is crucial for modern solar energy systems. This comprehensive guide explores the key aspects and benefits of ${selectedKeyword} implementation.</p>`,
          wordCount: 25
        },
        {
          id: 'main',
          type: 'content',
          content: `<h2 style='color: #FBD46F; font-family: Roboto; font-weight: 600;'>Key Benefits of ${selectedKeyword}</h2>
<p><b>${selectedKeyword}</b> offers numerous advantages for solar energy systems. Professional implementation ensures optimal performance and long-term reliability.</p>`,
          wordCount: 30
        }
      ],
      featureImage: this.getFallbackFeatureImage(selectedKeyword),
      contentImages: this.getFallbackContentImages(selectedKeyword),
      conclusion: this.getFallbackConclusion(selectedKeyword, companyName),
      estimatedRankMathScore: 75,
      generationTime,
      source: 'fallback',
      generatedAt: new Date().toISOString()
    };
  }

  /**
   * Clear caches
   */
  clearCache(type = 'all') {
    if (type === 'all' || type === 'content') {
      this.contentCache.flushAll();
      console.log('🧹 Content cache cleared');
    }
    if (type === 'all' || type === 'meta') {
      this.metaCache.flushAll();
      console.log('🧹 Meta cache cleared');
    }
    if (type === 'all' || type === 'images') {
      this.imageCache.flushAll();
      console.log('🧹 Image cache cleared');
    }
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return {
      content: {
        keys: this.contentCache.keys().length,
        hits: this.contentCache.getStats().hits,
        misses: this.contentCache.getStats().misses
      },
      meta: {
        keys: this.metaCache.keys().length,
        hits: this.metaCache.getStats().hits,
        misses: this.metaCache.getStats().misses
      },
      images: {
        keys: this.imageCache.keys().length,
        hits: this.imageCache.getStats().hits,
        misses: this.imageCache.getStats().misses
      }
    };
  }

  /**
   * Create image placeholder for on-demand generation
   */
  createImagePlaceholder(type, keyword, title = '') {
    return {
      success: false,
      imageUrl: null,
      placeholder: true,
      type: type,
      keyword: keyword,
      title: title,
      message: 'Image will be generated on-demand',
      generateOnDemand: true
    };
  }

  /**
   * Create multiple image placeholders
   */
  createImagePlaceholders(type, keyword, count = 2) {
    const placeholders = [];
    for (let i = 0; i < count; i++) {
      placeholders.push({
        success: false,
        imageUrl: null,
        placeholder: true,
        type: `${type}-${i + 1}`,
        keyword: keyword,
        message: `${type} image ${i + 1} will be generated on-demand`,
        generateOnDemand: true
      });
    }
    return placeholders;
  }
}

module.exports = new PerformanceOptimizationService();
